# 🔔 Notiflair

> Plateforme intelligente de gestion et d'optimisation des notifications

## 📋 Vue d'ensemble

Notiflair est une solution complète pour gérer, personnaliser et optimiser les notifications multi-canaux. Le projet est organisé en plusieurs sous-projets pour une architecture modulaire et scalable.

## 🏗️ Architecture du Projet

```
Notiflair/
├── API/                 # Backend FastAPI + MySQL
├── Admin/               # Interface d'administration React
├── Mobile/              # Application mobile Flutter
└── docs/                # Documentation globale
```

## 🚀 Sous-projets

### 🔧 API (FastAPI)

Backend principal avec API REST, gestion des utilisateurs, notifications et analytics.

**Technologies :** FastAPI, SQLAlchemy, MySQL, Redis, JWT

**Fonctionnalités :**

- Authentification par device_id
- Gestion notifications multilingues avec fallback
- **Overrides par pays** : Configuration spécifique par pays (rrule, relevance, etc.)
- Système de synchronisation par snapshots
- Versioning sémantique des données
- Documentation automatique Swagger/OpenAPI

📖 [Documentation API](./API/README.md)

### 🎛️ Admin (React + Vite + Tailwind)

Interface d'administration moderne pour la gestion des notifications et monitoring.

**Technologies :** React 18, Vite, Tailwind CSS, React Router

**Fonctionnalités :**

- **Authentification intégrée** avec l'API FastAPI (JWT)
- **Dashboard** avec statistiques et vue d'ensemble
- **Gestion des templates** de notifications (4 types : Info, Urgent, Manqué, Complété)
- **Configuration des canaux** (Push, In-App, Email) avec statistiques d'usage
- **Paramétrage des timings** de rappel (5min, 15min, 1h, 1j, 1sem + personnalisé)
- **Interface responsive** adaptée mobile/desktop
- **Navigation intuitive** avec sidebar et protection des routes
- **Design moderne** avec Tailwind CSS et animations

📖 [Documentation Admin](./Admin/README.md)

### 📱 Mobile (Flutter)

Application mobile cross-platform pour la réception et gestion des notifications.

**Technologies :** Flutter, Dart, SQLite local

**Fonctionnalités :**

- Synchronisation offline-first
- Notifications locales et push
- Interface multilingue
- Gestion des préférences utilisateur
- Mode hors ligne complet

📖 [Documentation Mobile](./Mobile/README.md) _(à créer)_

## 🛠️ Développement

### Prérequis

- **Python 3.11+** (recommandé pour l'API)
- **Node.js 16+** (pour l'interface Admin)
- **MySQL 8.0+** (base de données principale)
- **Redis 6.0+** (cache et sessions - optionnel)
- **Flutter SDK 3.0+** (pour l'app mobile - à venir)
- **Docker** (optionnel pour le déploiement)

### Configuration VSCode

Le projet inclut une configuration VSCode optimisée :

- Extensions recommandées automatiquement installées
- Formatage automatique du code
- Configuration de debug pour chaque sous-projet
- Tâches automatisées (tests, build, etc.)

### Standards de Codage

📖 Consultez notre [guide des standards de codage](./docs/coding-standards.md) pour les conventions du projet.

## 🚀 Démarrage Rapide

### 1. Cloner le projet

```bash
git clone <repository-url>
cd Notiflair
```

### 2. Configuration de l'environnement

#### API FastAPI

```bash
# Installer les extensions VSCode recommandées
# (VSCode vous proposera automatiquement)

# Configurer l'API
cd API
python -m venv venv
source venv/bin/activate  # ou venv\Scripts\activate sur Windows
pip install -r requirements.txt

# Configuration base de données
cp .env.example .env
# Éditer .env avec vos paramètres MySQL
```

#### Admin React

```bash
cd Admin
npm install

# Configuration (optionnel)
cp .env.example .env
# Éditer .env avec vos paramètres API si nécessaire
```

#### Mobile Flutter _(à venir)_

```bash
cd Mobile
flutter pub get
```

### 3. Configuration de la base de données

```bash
# Voir API/README.md pour les détails de configuration MySQL
# Création de la base et des tables avec architecture optimisée
```

#### 🔄 Architecture Base de Données (Post-Migration 2025)

Notiflair utilise une **architecture de base de données optimisée** avec pivot central pour simplifier la gestion des traductions multilingues et multi-pays :

**Avant Migration :** Clés composites complexes (pays + langue + entité)

```sql
-- Structure complexe AVANT
t_notif_trans_override_nov (ntf_id, cty_id, lng_id) -- Triple clé composite
```

**Après Migration :** Pivot central `ctl_id` (Country-Language)

```sql
-- Structure simplifiée APRÈS
tj_country_languages_ctl (ctl_id) → t_notif_translations_ntr (ctl_id) → t_notif_trans_override_nov (ntr_id)
```

**Avantages de la nouvelle architecture :**

- ✅ **Overrides ultra-simples** : Une seule FK au lieu de clés composites
- ✅ **Performance améliorée** : Moins de jointures, index plus efficaces
- ✅ **Cohérence garantie** : Combinaisons pays-langue centralisées
- ✅ **Maintenance facilitée** : Structure évolutive et lisible
- ✅ **Développement simplifié** : Logique métier plus claire

📖 [Documentation complète de la base de données](./API/docs/database.md)

### 4. Lancer le développement

#### API

```bash
# Option A : Script automatique (recommandé)
cd API
python start_dev.py

# Option B : Manuel
cd API
uvicorn main:app --reload

# Option C : VSCode
# Ctrl+Shift+P > "Tasks: Run Task" > "Start FastAPI Dev Server"

# API disponible sur http://localhost:8000
# Documentation sur http://localhost:8000/docs
# Health check sur http://localhost:8000/health
```

#### Admin

```bash
cd Admin
npm run dev
# Interface admin sur http://localhost:5173

# Authentification intégrée avec l'API :
# Utilisez les identifiants de la base de données
# Exemple : admin / secret
```

#### Mobile _(à venir)_

```bash
cd Mobile
flutter run
```

## 🔗 Intégration API ↔ Admin

L'interface Admin est maintenant **entièrement intégrée** avec l'API FastAPI :

### ✅ Fonctionnalités intégrées

- **Authentification JWT** : Connexion sécurisée via l'API
- **Gestion des sessions** : Tokens avec expiration automatique
- **Protection des routes** : Vérification d'authentification en temps réel
- **Gestion des erreurs** : Messages d'erreur contextuels de l'API
- **Déconnexion propre** : Invalidation des tokens côté serveur

### 🔧 Configuration

1. **Démarrer l'API** : `cd API && python start_dev.py`
2. **Démarrer l'Admin** : `cd Admin && npm run dev`
3. **Se connecter** avec les identifiants de la base de données

### 🌐 URLs de développement

- **API** : http://localhost:8000
- **Admin** : http://localhost:5173
- **Documentation API** : http://localhost:8000/docs
- **Health Check** : http://localhost:8000/health

## 📚 Documentation

- [Standards de codage](./docs/coding-standards.md)
- [Architecture générale](./docs/architecture.md) _(à venir)_
- [Guide de déploiement](./docs/deployment.md) _(à venir)_
- [API Documentation](./API/README.md)
- [Admin Documentation](./Admin/README.md)
- [Mobile Documentation](./Mobile/README.md) _(à venir)_
- [Base de données](./API/docs/database.md)

## 🤝 Contribution

1. Consultez les [standards de codage](./docs/coding-standards.md)
2. Créez une branche pour votre fonctionnalité
3. Suivez les conventions de commit
4. Ajoutez des tests pour vos modifications
5. Soumettez une Pull Request

## 📄 Licence

_À définir_

## 🆘 Support

Pour toute question ou problème :

- Consultez la documentation du sous-projet concerné
- Créez une issue sur le repository
- Contactez l'équipe de développement

---

## 📝 Changelog

### v1.1.0 - Overrides par pays (2024-01-15)

**🆕 Nouvelles fonctionnalités :**

- **Table d'overrides par pays** : `t_ntf_cty_override_nco` pour configurations spécifiques
- **Endpoints API** : CRUD complet pour la gestion des overrides par pays
- **Logique de fallback** : Utilisation automatique des overrides quand disponibles
- **Documentation** : Mise à jour complète de la documentation technique

**🔧 Améliorations techniques :**

- Modèles SQLAlchemy étendus avec `NotificationCountryOverride`
- Schémas Pydantic pour validation des overrides
- Index de performance pour les requêtes d'overrides
- Exemples d'utilisation SQL et API

**📚 Documentation :**

- Nouveaux endpoints documentés dans `API/docs/endpoints-notifications.md`
- Exemples SQL ajoutés dans `API/docs/database.md`
- Architecture d'overrides expliquée avec exemples pratiques

---

## 📊 Status du Projet

| Composant          | Status              | Version | Fonctionnalités                          |
| ------------------ | ------------------- | ------- | ---------------------------------------- |
| **API FastAPI**    | ✅ **Opérationnel** | v1.1.0  | Auth, Notifications, Overrides, DB, Docs |
| **Admin React**    | ✅ **Opérationnel** | v2.1.0  | Auth intégrée, Dashboard, Templates      |
| **Mobile Flutter** | 🚧 **À venir**      | -       | Planifié                                 |
| **Intégration**    | ✅ **Complète**     | -       | API ↔ Admin fonctionnelle                |

**Status global :** 🚀 **Phase 1 complète** - API + Admin opérationnels
