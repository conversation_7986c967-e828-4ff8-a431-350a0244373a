#!/usr/bin/env python3
"""
Script de test pour les nouveaux endpoints d'overrides par pays
"""
import requests
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/v1/notifications"

def test_get_notification_data():
    """Récupère des données de test"""
    print("🔍 Récupération des données de test...")
    
    # Récupérer la hiérarchie pour avoir des IDs
    response = requests.get(f"{BASE_URL}/hierarchy", params={
        "country_code": "BE",
        "language_code": "fr"
    })
    
    if response.status_code != 200:
        print(f"❌ Erreur lors de la récupération de la hiérarchie: {response.status_code}")
        return None, None
    
    data = response.json()
    print(f"Nombre de types dans la hiérarchie: {len(data['hierarchy'])}")

    # Trouver la première notification
    for i, type_data in enumerate(data['hierarchy']):
        print(f"Type {i}: {type_data['notification_type']['ntp_identifier_key']} - {len(type_data['notifications'])} notifications")
        if type_data['notifications']:
            first_notif = type_data['notifications'][0]
            print(f"✅ Notification trouvée: {first_notif['ntf_identifier_key']} (ID: {first_notif['ntf_id']})")
            return first_notif['ntf_id'], first_notif['ntf_identifier_key']

    print("❌ Aucune notification trouvée dans les types")
    return None, None

def test_get_country_overrides():
    """Test GET /overrides/country/{country_code}"""
    print("\n🧪 Test: GET /overrides/country/BE")
    
    response = requests.get(f"{BASE_URL}/overrides/country/BE")
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Overrides trouvés: {len(data)}")
        if data:
            print(f"Premier override: {json.dumps(data[0], indent=2)}")
        else:
            print("Aucun override existant pour la Belgique")
    else:
        print(f"❌ Erreur: {response.text}")

def test_get_notification_overrides(ntf_id: int):
    """Test GET /{notification_id}/overrides"""
    print(f"\n🧪 Test: GET /{ntf_id}/overrides")
    
    response = requests.get(f"{BASE_URL}/{ntf_id}/overrides")
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Overrides pour notification {ntf_id}: {len(data)}")
        if data:
            print(f"Premier override: {json.dumps(data[0], indent=2)}")
        else:
            print("Aucun override pour cette notification")
    else:
        print(f"❌ Erreur: {response.text}")

def test_create_override(ntf_id: int):
    """Test POST /overrides"""
    print(f"\n🧪 Test: POST /overrides (notification {ntf_id})")
    
    # Données de test pour créer un override
    override_data = {
        "ntf_id": ntf_id,
        "cty_id": 1,  # Belgique (supposé)
        "nco_is_active": True,
        "nco_relevance_score": 8.5,
        "nco_usage_count": 100,
        "nco_needs_annual_update": True,
        "nco_last_updated_year": 2024,
        "nco_default_reminder": {"time": "09:00", "days_before": 7}
    }
    
    response = requests.post(f"{BASE_URL}/overrides", json=override_data)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 201:
        data = response.json()
        print(f"✅ Override créé avec ID: {data['nco_id']}")
        print(f"Données: {json.dumps(data, indent=2)}")
        return data['nco_id']
    elif response.status_code == 409:
        print("⚠️ Override déjà existant (normal)")
        return None
    else:
        print(f"❌ Erreur: {response.text}")
        return None

def test_get_notification_with_overrides(ntf_id: int):
    """Test GET /{notification_id}/with-overrides"""
    print(f"\n🧪 Test: GET /{ntf_id}/with-overrides")

    response = requests.get(f"{BASE_URL}/{ntf_id}/with-overrides")
    print(f"Status: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Notification avec overrides récupérée")
        print(f"Notification ID: {data['notification']['ntf_id']}")
        print(f"Nombre d'overrides: {len(data['country_overrides'])}")
        if data['country_overrides']:
            print(f"Premier override: {json.dumps(data['country_overrides'][0], indent=2)}")
    else:
        print(f"❌ Erreur: {response.text}")

def test_update_override(override_id: int):
    """Test PUT /overrides/{override_id}"""
    print(f"\n🧪 Test: PUT /overrides/{override_id}")

    # Données de mise à jour
    update_data = {
        "nco_relevance_score": 9.0,
        "nco_usage_count": 200,
        "nco_rrule": "FREQ=DAILY;BYHOUR=8",
        "nco_rrule_text": "Tous les jours à 8h"
    }

    response = requests.put(f"{BASE_URL}/overrides/{override_id}", json=update_data)
    print(f"Status: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"✅ Override mis à jour")
        print(f"Nouveau score: {data['nco_relevance_score']}")
        print(f"Nouveau usage_count: {data['nco_usage_count']}")
        print(f"Nouvelle rrule: {data['nco_rrule']}")
    else:
        print(f"❌ Erreur: {response.text}")

def test_delete_override(override_id: int):
    """Test DELETE /overrides/{override_id}"""
    print(f"\n🧪 Test: DELETE /overrides/{override_id}")

    response = requests.delete(f"{BASE_URL}/overrides/{override_id}")
    print(f"Status: {response.status_code}")

    if response.status_code == 204:
        print(f"✅ Override supprimé avec succès")
    else:
        print(f"❌ Erreur: {response.text}")

    # Vérifier que l'override n'existe plus
    print(f"🔍 Vérification de la suppression...")
    response = requests.get(f"{BASE_URL}/overrides/{override_id}")
    if response.status_code == 404:
        print(f"✅ Confirmation: override {override_id} n'existe plus")
    else:
        print(f"⚠️ Override encore présent: {response.status_code}")

def main():
    """Fonction principale de test"""
    print("🚀 Test des endpoints d'overrides par pays")
    print("=" * 50)

    # Utiliser un ID de notification connu de la base
    ntf_id = 83  # daily_medication
    print(f"🔧 Utilisation de l'ID de notification: {ntf_id}")

    # Récupérer des données de test (optionnel)
    # ntf_id, ntf_key = test_get_notification_data()
    # if not ntf_id:
    #     print("❌ Impossible de continuer sans données de test")
    #     return
    
    # Tester tous les endpoints
    test_get_country_overrides()
    test_get_notification_overrides(ntf_id)
    
    # Créer un override de test
    override_id = test_create_override(ntf_id)

    # Tester la récupération avec overrides
    test_get_notification_with_overrides(ntf_id)

    # Tester la modification si un override a été créé
    if override_id:
        test_update_override(override_id)

        # Re-tester la récupération après modification
        test_get_notification_with_overrides(ntf_id)

        # Tester la suppression
        test_delete_override(override_id)

    print("\n✅ Tests terminés!")

if __name__ == "__main__":
    main()
