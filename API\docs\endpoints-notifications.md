# 🔔 Endpoints de Notifications - Documentation

## Vue d'ensemble

Les endpoints de notifications permettent de consulter la hiérarchie complète des notifications et leurs keywords associés, avec support complet des traductions par pays et langue.

## Endpoints disponibles

### 🌳 GET `/api/v1/notifications/hierarchy`

Retourne la hiérarchie complète des notifications organisées par type/sous-type/sous-sous-type.

**Paramètres obligatoires :**

- `country_code` (string) : Code ISO 3166-1 alpha-2 du pays (ex: "BE", "FR")
- `language_code` (string) : Code ISO 639-1 de la langue (ex: "fr", "en")

**Paramètres optionnels :**

- `include_inactive` (bool) : Inclure les notifications inactives (défaut: false)

**Exemple :** `GET /api/v1/notifications/hierarchy?country_code=BE&language_code=fr`

**Réponse :**

```json
{
  "hierarchy": [
    {
      "notification_type": {
        "ntp_id": 59,
        "ntp_identifier_key": "health_wellness",
        "ntp_icon": "medical-kit-outline",
        "ntp_color": "#E74C3C",
        "ntp_bg_color": "#FFEBEE",
        "ntp_display_order": 1,
        "ntp_parent_id": null,
        "ntp_level": 0,
        "ntp_description_template": null,
        "ntp_default_enabled": true,
        "label": "Santé et bien-être",
        "description": "Notifications liées à la santé",
        "translation_source": "default",
        "children": [
          {
            "ntp_id": 60,
            "ntp_identifier_key": "medical_appointments",
            "ntp_level": 1,
            "ntp_parent_id": 59,
            "label": "Rendez-vous médicaux",
            "translation_source": "default",
            "children": []
          }
        ]
      },
      "notifications": [
        {
          "ntf_id": 123,
          "cty_id": 2,
          "ntp_id": 59,
          "ntf_identifier_key": "annual_checkup",
          "ntf_is_active": true,
          "ntf_rrule": "FREQ=YEARLY",
          "ntf_rrule_text": "Annuel",
          "ntf_icon": null,
          "ntf_usage_count": 0,
          "ntf_needs_annual_update": false,
          "ntf_last_updated_year": null,
          "ntf_default_reminder": null,
          "ntf_relevance_score": 8.5,
          "label": "Bilan de santé annuel",
          "description": "Contrôle médical préventif",
          "translation_source": "default",
          "country_code": "BE",
          "country_name": "Belgique"
        }
      ],
      "children": [
        {
          "notification_type": {
            /* sous-type */
          },
          "notifications": [
            /* notifications du sous-type */
          ],
          "children": [
            /* sous-sous-types */
          ]
        }
      ]
    }
  ],
  "country_code": "BE",
  "language_code": "fr",
  "total_types": 45,
  "total_notifications": 156,
  "active_notifications": 142
}
```

**Caractéristiques :**

- ✅ Hiérarchie complète type/sous-type/sous-sous-type
- ✅ Toutes les données des types ET des notifications
- ✅ Système de traductions avec fallback (override → défaut → clé technique)
- ✅ **SANS les keywords** (pour éviter un fichier trop volumineux)
- ✅ Notifications globales + spécifiques au pays

### 🏷️ GET `/api/v1/notifications/keywords`

Retourne les keywords des notifications avec leurs traductions.

**Paramètres obligatoires :**

- `country_code` (string) : Code ISO 3166-1 alpha-2 du pays (ex: "BE", "FR")
- `language_code` (string) : Code ISO 639-1 de la langue (ex: "fr", "en")

**Paramètres optionnels :**

- `notification_id` (int) : ID d'une notification spécifique
- `include_inactive` (bool) : Inclure les notifications inactives (défaut: false)

**Comportement :**

- Si `notification_id` **non spécifié** → retourne tous les keywords de toutes les notifications du pays/langue
- Si `notification_id` **spécifié** → retourne uniquement les keywords de cette notification

**Exemples :**

1. **Tous les keywords :** `GET /api/v1/notifications/keywords?country_code=BE&language_code=fr`
2. **Keywords d'une notification :** `GET /api/v1/notifications/keywords?country_code=BE&language_code=fr&notification_id=123`

**Réponse :**

```json
{
  "country_code": "BE",
  "language_code": "fr",
  "notifications": [
    {
      "ntf_id": 123,
      "ntf_identifier_key": "annual_checkup",
      "ntf_label": "Bilan de santé annuel",
      "keywords": [
        {
          "kwd_id": 45,
          "kwd_identifier": "health",
          "lng_id": 1,
          "lng_code": "fr",
          "kwt_term": "santé"
        },
        {
          "kwd_id": 67,
          "kwd_identifier": "medical",
          "lng_id": 1,
          "lng_code": "fr",
          "kwt_term": "médical"
        }
      ]
    }
  ],
  "total_notifications": 156,
  "total_keywords": 89
}
```

## Structure des données

### Type de notification (NotificationType)

| Champ                      | Type    | Description                    |
| -------------------------- | ------- | ------------------------------ |
| `ntp_id`                   | integer | Identifiant unique du type     |
| `ntp_identifier_key`       | string  | Clé technique unique           |
| `ntp_icon`                 | string  | Nom de l'icône                 |
| `ntp_color`                | string  | Couleur hexa (#FF0000)         |
| `ntp_bg_color`             | string  | Couleur de fond                |
| `ntp_display_order`        | integer | Ordre d'affichage              |
| `ntp_parent_id`            | integer | ID parent pour hiérarchie      |
| `ntp_level`                | integer | Niveau (0=racine, 1=enfant...) |
| `ntp_description_template` | string  | Template de description        |
| `ntp_default_enabled`      | boolean | Activé par défaut              |
| `label`                    | string  | Nom traduit (avec fallback)    |
| `description`              | string  | Description traduite           |
| `translation_source`       | string  | Source de la traduction        |
| `children`                 | array   | Types enfants                  |

### Notification

| Champ                     | Type    | Description                  |
| ------------------------- | ------- | ---------------------------- |
| `ntf_id`                  | integer | Identifiant unique           |
| `cty_id`                  | integer | ID pays (NULL = global)      |
| `ntp_id`                  | integer | ID du type de notification   |
| `ntf_identifier_key`      | string  | Clé unique en anglais        |
| `ntf_is_active`           | boolean | Statut actif/inactif         |
| `ntf_rrule`               | string  | Règle RFC 5545 (iCalendar)   |
| `ntf_rrule_text`          | string  | Description humaine          |
| `ntf_icon`                | string  | Override de l'icône          |
| `ntf_usage_count`         | integer | Compteur d'utilisation       |
| `ntf_needs_annual_update` | boolean | Mise à jour annuelle requise |
| `ntf_last_updated_year`   | integer | Dernière année mise à jour   |
| `ntf_default_reminder`    | object  | Config rappel JSON           |
| `ntf_relevance_score`     | float   | Score 0.00-9.99              |
| `label`                   | string  | Nom traduit (avec fallback)  |
| `description`             | string  | Description traduite         |
| `translation_source`      | string  | Source de la traduction      |
| `country_code`            | string  | Code du pays                 |
| `country_name`            | string  | Nom du pays                  |

### Keyword

| Champ            | Type    | Description           |
| ---------------- | ------- | --------------------- |
| `kwd_id`         | integer | Identifiant unique    |
| `kwd_identifier` | string  | Identifiant technique |
| `lng_id`         | integer | ID de la langue       |
| `lng_code`       | string  | Code de la langue     |
| `kwt_term`       | string  | Terme traduit         |

## Système de traductions

Les endpoints utilisent un système de traductions sophistiqué avec fallback :

1. **Override par pays** : Traduction spécifique au pays/langue
2. **Traduction par défaut** : Traduction générale pour la langue
3. **Fallback** : Clé technique si aucune traduction

Le champ `translation_source` indique la source utilisée :

- `"override"` : Traduction spécifique au pays
- `"default"` : Traduction par défaut
- `"fallback"` : Clé technique utilisée

## Exemples d'utilisation

### Curl

```bash
# Hiérarchie complète pour la Belgique en français
curl -X GET "http://localhost:8000/api/v1/notifications/hierarchy?country_code=BE&language_code=fr" \
     -H "accept: application/json"

# Tous les keywords pour la Belgique en français
curl -X GET "http://localhost:8000/api/v1/notifications/keywords?country_code=BE&language_code=fr" \
     -H "accept: application/json"

# Keywords d'une notification spécifique
curl -X GET "http://localhost:8000/api/v1/notifications/keywords?country_code=BE&language_code=fr&notification_id=123" \
     -H "accept: application/json"
```

### Python

```python
import requests

base_url = "http://localhost:8000/api/v1/notifications"

# Récupérer la hiérarchie
response = requests.get(f"{base_url}/hierarchy", params={
    "country_code": "BE",
    "language_code": "fr"
})
hierarchy_data = response.json()

print(f"Pays: {hierarchy_data['country_code']}")
print(f"Langue: {hierarchy_data['language_code']}")
print(f"Types: {hierarchy_data['total_types']}")
print(f"Notifications: {hierarchy_data['total_notifications']}")

# Parcourir la hiérarchie
for root_type in hierarchy_data['hierarchy']:
    type_info = root_type['notification_type']
    print(f"\n📁 {type_info['label']} ({type_info['ntp_identifier_key']})")

    # Notifications de ce type
    for notification in root_type['notifications']:
        print(f"  📄 {notification['label']}")

    # Sous-types
    for child in root_type['children']:
        child_type = child['notification_type']
        print(f"  📁 {child_type['label']}")

# Récupérer les keywords
keywords_response = requests.get(f"{base_url}/keywords", params={
    "country_code": "BE",
    "language_code": "fr"
})
keywords_data = keywords_response.json()

print(f"\n🏷️ Keywords pour {keywords_data['total_notifications']} notifications:")
for notification in keywords_data['notifications']:
    if notification['keywords']:
        print(f"  {notification['ntf_label']}: {[kw['kwt_term'] for kw in notification['keywords']]}")
```

### JavaScript

```javascript
// Récupérer la hiérarchie
async function getNotificationHierarchy(countryCode, languageCode) {
  const response = await fetch(
    `/api/v1/notifications/hierarchy?country_code=${countryCode}&language_code=${languageCode}`
  );
  const data = await response.json();

  console.log(`Hiérarchie pour ${data.country_code}/${data.language_code}`);
  console.log(
    `${data.total_types} types, ${data.total_notifications} notifications`
  );

  return data;
}

// Récupérer les keywords d'une notification
async function getNotificationKeywords(
  countryCode,
  languageCode,
  notificationId = null
) {
  let url = `/api/v1/notifications/keywords?country_code=${countryCode}&language_code=${languageCode}`;
  if (notificationId) {
    url += `&notification_id=${notificationId}`;
  }

  const response = await fetch(url);
  const data = await response.json();

  console.log(`Keywords pour ${data.total_notifications} notifications`);
  return data;
}

// Utilisation
getNotificationHierarchy("BE", "fr").then((hierarchy) => {
  // Traiter la hiérarchie
  hierarchy.hierarchy.forEach((rootType) => {
    console.log(`Type: ${rootType.notification_type.label}`);
    rootType.notifications.forEach((notif) => {
      console.log(`  - ${notif.label}`);
    });
  });
});
```

## Codes d'erreur

- `400` : Paramètres manquants ou invalides
- `404` : Pays, langue ou notification non trouvé(e)
- `500` : Erreur serveur

## Performance

- **Optimisations** : Eager loading pour éviter le problème N+1
- **Cache** : SQLAlchemy met en cache les requêtes répétées
- **Pagination** : Pas de pagination (données de référence)
- **Taille** : Hiérarchie ~128KB, Keywords ~16KB pour la Belgique

## Endpoints pour les overrides par pays

### GET /notifications/overrides/country/{country_code}

Récupère tous les overrides de notifications pour un pays donné.

**Paramètres :**

- `country_code` (path) : Code ISO du pays (FR, BE...)

**Réponse :** `200 OK`

```json
[
  {
    "nco_id": 1,
    "ntf_id": 123,
    "cty_id": 1,
    "nco_is_active": true,
    "nco_rrule": "FREQ=YEARLY;BYMONTH=1;BYMONTHDAY=1",
    "nco_rrule_text": "Chaque 1er janvier",
    "nco_usage_count": 150,
    "nco_needs_annual_update": true,
    "nco_last_updated_year": 2024,
    "nco_default_reminder": { "time": "09:00", "days_before": 7 },
    "nco_relevance_score": 8.5,
    "nco_created_at": "2024-01-15T10:30:00Z",
    "nco_updated_at": "2024-01-15T10:30:00Z"
  }
]
```

### GET /notifications/{notification_id}/overrides

Récupère tous les overrides par pays pour une notification donnée.

**Paramètres :**

- `notification_id` (path) : ID de la notification

**Réponse :** `200 OK` - Liste des overrides (même format que ci-dessus)

### POST /notifications/overrides

Crée un nouvel override de notification pour un pays spécifique.

**Corps de la requête :**

```json
{
  "ntf_id": 123,
  "cty_id": 1,
  "nco_is_active": true,
  "nco_rrule": "FREQ=YEARLY;BYMONTH=1;BYMONTHDAY=1",
  "nco_rrule_text": "Chaque 1er janvier",
  "nco_usage_count": 0,
  "nco_needs_annual_update": true,
  "nco_last_updated_year": 2024,
  "nco_default_reminder": { "time": "09:00", "days_before": 7 },
  "nco_relevance_score": 8.5
}
```

**Réponse :** `201 Created` - Override créé (même format que GET)

### PUT /notifications/overrides/{override_id}

Modifie un override de notification existant.

**Paramètres :**

- `override_id` (path) : ID de l'override

**Corps de la requête :** Mêmes champs que POST (tous optionnels)

**Réponse :** `200 OK` - Override modifié

### DELETE /notifications/overrides/{override_id}

Supprime un override de notification.

**Paramètres :**

- `override_id` (path) : ID de l'override

**Réponse :** `204 No Content`

### GET /notifications/{notification_id}/with-overrides

Récupère une notification avec tous ses overrides par pays.

**Paramètres :**

- `notification_id` (path) : ID de la notification

**Réponse :** `200 OK`

```json
{
  "notification": {
    "ntf_id": 123,
    "ntf_identifier_key": "new_year",
    "ntf_is_active": true,
    "ntf_rrule": "FREQ=YEARLY;BYMONTH=1;BYMONTHDAY=1",
    "ntf_relevance_score": 7.0
  },
  "country_overrides": [
    {
      "nco_id": 1,
      "ntf_id": 123,
      "cty_id": 1,
      "nco_relevance_score": 8.5
    }
  ]
}
```

## Documentation interactive

L'API fournit une documentation interactive Swagger accessible à :

- **Swagger UI** : http://localhost:8000/docs
- **ReDoc** : http://localhost:8000/redoc
