-- =====================================================
-- Script de création de la table d'override par pays
-- =====================================================
-- 
-- Ce script crée la table t_ntf_cty_override_nco pour gérer
-- les spécificités des notifications par pays
--
-- Architecture :
-- - t_notifications_ntf : Valeurs par défaut globales
-- - t_ntf_cty_override_nco : Overrides spécifiques par pays
--
-- ATTENTION : Faire une sauvegarde avant d'exécuter !
--

-- =====================================================
-- 1. CRÉATION DE LA TABLE D'OVERRIDE
-- =====================================================

CREATE TABLE IF NOT EXISTS t_ntf_cty_override_nco (
    -- Clé primaire auto-increment
    nco_id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Clés étrangères
    ntf_id INT NOT NULL,
    cty_id INT NOT NULL,
    
    -- Champs d'override (tous optionnels)
    nco_is_active BOOLEAN NULL COMMENT 'Override du statut actif/inactif',
    nco_rrule VARCHAR(500) NULL COMMENT 'Override de la règle RFC 5545',
    nco_rrule_text TEXT NULL COMMENT 'Override de la description humaine',
    nco_usage_count INT NULL DEFAULT 0 COMMENT 'Compteur d\'utilisation spécifique au pays',
    nco_needs_annual_update BOOLEAN NULL COMMENT 'Override du besoin de mise à jour annuelle',
    nco_last_updated_year INT NULL COMMENT 'Dernière année de mise à jour pour ce pays',
    nco_default_reminder JSON NULL COMMENT 'Override de la config rappel {time, days_before}',
    nco_relevance_score DECIMAL(3,2) NULL COMMENT 'Override du score de pertinence 0.00-9.99',
    
    -- Métadonnées
    nco_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    nco_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Contraintes
    CONSTRAINT fk_nco_ntf_id FOREIGN KEY (ntf_id) REFERENCES t_notifications_ntf(ntf_id) ON DELETE CASCADE,
    CONSTRAINT fk_nco_cty_id FOREIGN KEY (cty_id) REFERENCES tr_countries_cty(cty_id) ON DELETE CASCADE,
    
    -- Index unique pour éviter les doublons
    UNIQUE KEY uk_ntf_cty_override (ntf_id, cty_id),
    
    -- Index pour les performances
    INDEX idx_nco_ntf_id (ntf_id),
    INDEX idx_nco_cty_id (cty_id),
    INDEX idx_nco_active (nco_is_active),
    INDEX idx_nco_relevance (nco_relevance_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Table d\'override des notifications par pays - spécificités locales';

-- =====================================================
-- 2. VÉRIFICATION DE LA STRUCTURE
-- =====================================================

-- Afficher la structure de la nouvelle table
DESCRIBE t_ntf_cty_override_nco;

-- Vérifier les contraintes
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 't_ntf_cty_override_nco' 
AND TABLE_SCHEMA = DATABASE();

-- =====================================================
-- 3. DONNÉES DE TEST (OPTIONNEL)
-- =====================================================

-- Exemple d'insertion d'un override pour la France
-- (Décommentez si vous voulez tester)

/*
INSERT INTO t_ntf_cty_override_nco (
    ntf_id, 
    cty_id, 
    nco_is_active, 
    nco_relevance_score,
    nco_default_reminder
) VALUES (
    1,  -- ID d'une notification existante
    1,  -- ID de la France (à adapter selon vos données)
    true,
    8.50,
    JSON_OBJECT('time', '09:00', 'days_before', 7)
);
*/

-- =====================================================
-- 4. REQUÊTES DE VÉRIFICATION
-- =====================================================

-- Compter les notifications par pays (devrait être 0 au début)
SELECT 
    c.cty_name,
    c.cty_code,
    COUNT(nco.nco_id) as nb_overrides
FROM tr_countries_cty c
LEFT JOIN t_ntf_cty_override_nco nco ON c.cty_id = nco.cty_id
GROUP BY c.cty_id, c.cty_name, c.cty_code
ORDER BY nb_overrides DESC, c.cty_name;

-- Vérifier la structure des notifications existantes
SELECT 
    COUNT(*) as total_notifications,
    COUNT(CASE WHEN cty_id IS NOT NULL THEN 1 END) as country_specific,
    COUNT(CASE WHEN cty_id IS NULL THEN 1 END) as global_notifications
FROM t_notifications_ntf;

-- =====================================================
-- 5. COMMENTAIRES SUR L'ARCHITECTURE
-- =====================================================

/*
LOGIQUE D'UTILISATION :

1. VALEURS PAR DÉFAUT :
   - Toutes les valeurs dans t_notifications_ntf servent de base
   - Si pas d'override, utiliser ces valeurs

2. OVERRIDES PAR PAYS :
   - Créer une entrée dans t_ntf_cty_override_nco seulement si nécessaire
   - Seuls les champs non-NULL dans l'override remplacent les valeurs par défaut
   - Les champs NULL dans l'override utilisent la valeur par défaut

3. EXEMPLE DE REQUÊTE AVEC FALLBACK :
   SELECT 
       n.ntf_id,
       n.ntf_identifier_key,
       COALESCE(nco.nco_is_active, n.ntf_is_active) as is_active,
       COALESCE(nco.nco_rrule, n.ntf_rrule) as rrule,
       COALESCE(nco.nco_relevance_score, n.ntf_relevance_score) as relevance_score
   FROM t_notifications_ntf n
   LEFT JOIN t_ntf_cty_override_nco nco ON n.ntf_id = nco.ntf_id AND nco.cty_id = ?
   WHERE n.ntf_id = ?;

4. AVANTAGES :
   - Économie de stockage (pas d'entrée si pas d'override)
   - Flexibilité totale par pays
   - Cohérence avec le pattern des traductions
   - Performance optimisée avec les index
*/

-- =====================================================
-- 6. SCRIPT TERMINÉ
-- =====================================================

SELECT 'Migration terminée : Table t_ntf_cty_override_nco créée avec succès !' as status;
