# 📚 Notiflair Database Documentation

## 📋 Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture de la base de données](#architecture-de-la-base-de-données)
3. [Convention de nommage](#convention-de-nommage)
4. [Système de traduction](#système-de-traduction)
5. [Système de synchronisation](#système-de-synchronisation)
6. [Structure des tables](#structure-des-tables)
7. [Migration et maintenance](#migration-et-maintenance)

## 🎯 Vue d'ensemble

Notiflair est un système de gestion de notifications/rappels avec architecture multilingue et multi-pays. La base de données MySQL sert de source de vérité pour l'application mobile qui utilise SQLite en local.

### Caractéristiques techniques

- **Architecture multilingue optimisée** avec pivot central `ctl_id`
- **Système d'override simplifié** via références directes
- **Synchronisation offline-first** via snapshots compressés
- **Versioning sémantique** des données
- **Convention ISO SQL:2013** adaptée

### 🚀 Migration 2025 : Pivot Central

**Problème résolu :** Les clés composites multiples (pays + langue + entité) créaient une complexité de maintenance et des performances dégradées.

**Solution adoptée :** Centralisation des combinaisons pays-langue via un identifiant unique `ctl_id` dans `tj_country_languages_ctl`.

**Résultats :**

- **Performance** : Réduction des jointures de 3 à 1 table
- **Simplicité** : Overrides avec une seule FK au lieu de clés composites
- **Cohérence** : Garantie des combinaisons pays-langue valides
- **Évolutivité** : Structure facilement extensible

## 🏗️ Architecture de la base de données

### Design Pattern : Versioned Snapshot Pattern

```
MySQL (Master) → Snapshots compressés → SQLite (Mobile)
```

- **MySQL** : Source de vérité centralisée
- **Snapshots** : États versionnés pré-calculés en JSON compressé
- **SQLite** : Base locale simplifiée en lecture seule

### Flux de synchronisation

```
1. Check version : Mobile → API → Compare versions
2. Si update needed : API → Generate/Retrieve snapshot
3. Download : API → GZIP JSON → Mobile
4. Import : Mobile → Replace SQLite
```

## 📏 Convention de nommage

Basée sur ISO SQL:2013, adaptée pour MySQL case-insensitive :

### Préfixes des tables

- **`tr_`** : Tables de référence (données statiques)
- **`t_`** : Tables de données (données métier)
- **`tj_`** : Tables de jointure (relations N:N)

### Suffixes des colonnes (3 lettres)

Chaque table a un code unique de 3 lettres utilisé comme préfixe pour ses colonnes :

```sql
-- Exemple : Table t_notifications_ntf
ntf_id           -- Préfixe NTF pour toutes les colonnes
ntf_identifier_key
ntf_is_active
```

### Format des noms

- Tables : `prefixe_nom_complet_code`
- Colonnes : `code_nom_colonne`
- Index : `idx_nom_descriptif`
- Contraintes : `fk_table_reference` ou `uk_nom_unique`

## 🌐 Système de traduction

### Architecture à 3 niveaux (Post-Migration)

1. **Traductions par défaut** : Une traduction par combinaison pays-langue (`ctl_id`)
2. **Overrides simplifiés** : Référence directe à la traduction (`ntr_id`)
3. **Fallback automatique** : Override → Défaut → Clé technique

### Pattern d'implémentation (Nouvelle Architecture)

```sql
-- 1. Pivot central pays-langue
tj_country_languages_ctl (
    ctl_id,          -- ID unique de la combinaison
    cty_id,          -- FK vers pays
    lng_id           -- FK vers langue
)

-- 2. Table de traduction avec pivot
t_[entity]_translations_[code] (
    [code]_id,       -- ID unique de la traduction
    ctl_id,          -- FK vers combinaison pays-langue (NULL = universel)
    entity_id,       -- FK vers entité
    label,
    description
)

-- 3. Table d'override ultra-simple
t_[entity]_trans_override_[code] (
    [override_code]_id,  -- ID unique de l'override
    [code]_id,           -- FK UNIQUE vers traduction
    label,
    description
)
```

### Avantages de la nouvelle architecture

- **Simplicité** : Override avec une seule FK au lieu de triple clé composite
- **Performance** : Moins de jointures (1 au lieu de 2-3)
- **Cohérence** : Combinaisons pays-langue garanties valides
- **Maintenance** : Structure évolutive et lisible

### Requête avec fallback (Nouvelle Architecture)

```sql
-- Récupération avec fallback automatique
SELECT
    COALESCE(override.label, translation.label, entity.identifier_key) as label,
    CASE
        WHEN override.label IS NOT NULL THEN 'override'
        WHEN translation.label IS NOT NULL THEN 'default'
        ELSE 'fallback'
    END as source
FROM entity_table entity
LEFT JOIN translation_table translation ON entity.id = translation.entity_id
LEFT JOIN override_table override ON translation.id = override.translation_id
WHERE translation.ctl_id = ? OR translation.ctl_id IS NULL
```

## 🔄 Système de synchronisation

### Tables impliquées

1. **`t_translation_versions_tvr`** : Versions par pays/langue
2. **`t_sync_snapshots_sss`** : Snapshots pré-calculés
3. **`v_mobile_export`** : Vue pour génération des données

### Structure d'un snapshot

```json
{
    "version": "v1.0.0",
    "country": "BE",
    "language": "fr",
    "generated": "2025-06-23T16:00:00Z",
    "notifications": [...],
    "translations": {...}
}
```

### Processus de versioning

- Format : `vMAJOR.MINOR.PATCH`
- Incrémentation automatique via `publish_translation_version()`
- Génération de snapshot à chaque publication

## 📊 Structure détaillée des tables

### Tables de référence (TR\_)

#### `tr_languages_lng`

Table de référence des langues disponibles.

| Colonne    | Type               | Description                            |
| ---------- | ------------------ | -------------------------------------- |
| `lng_id`   | INT AUTO_INCREMENT | Identifiant unique de la langue        |
| `lng_code` | VARCHAR(5)         | Code ISO 639-1 (fr, en, nl...)         |
| `lng_name` | VARCHAR(50)        | Nom de la langue dans sa propre langue |

#### `tr_countries_cty`

Table de référence des pays supportés.

| Colonne              | Type               | Description                           |
| -------------------- | ------------------ | ------------------------------------- |
| `cty_id`             | INT AUTO_INCREMENT | Identifiant unique du pays            |
| `cty_code`           | VARCHAR(2)         | Code ISO 3166-1 alpha-2 (FR, BE...)   |
| `cty_name`           | VARCHAR(100)       | Nom officiel du pays                  |
| `cty_timezone`       | VARCHAR(50)        | Fuseau horaire IANA (Europe/Paris...) |
| `cty_default_lng_id` | INT                | FK vers langue par défaut             |
| `cty_created_at`     | TIMESTAMP          | Date de création                      |
| `cty_updated_at`     | TIMESTAMP          | Dernière modification                 |

#### `tr_notif_types_ntp`

Types/catégories de notifications avec support de hiérarchie.

| Colonne                    | Type               | Description                     |
| -------------------------- | ------------------ | ------------------------------- |
| `ntp_id`                   | INT AUTO_INCREMENT | Identifiant unique              |
| `ntp_identifier_key`       | VARCHAR(50)        | Clé technique unique en anglais |
| `ntp_icon`                 | VARCHAR(50)        | Nom de l'icône (sans extension) |
| `ntp_color`                | VARCHAR(7)         | Couleur hexa (#FF0000)          |
| `ntp_bg_color`             | VARCHAR(7)         | Couleur de fond                 |
| `ntp_display_order`        | INT                | Ordre d'affichage               |
| `ntp_parent_id`            | INT                | ID parent pour hiérarchie       |
| `ntp_level`                | TINYINT            | Niveau (0=racine, 1=enfant...)  |
| `ntp_description_template` | TEXT               | Template de description         |
| `ntp_default_enabled`      | BOOLEAN            | Activé par défaut               |

#### `tr_keywords_kwd`

Concepts de keywords indépendants de la langue.

| Colonne          | Type               | Description                      |
| ---------------- | ------------------ | -------------------------------- |
| `kwd_id`         | INT AUTO_INCREMENT | Identifiant unique du concept    |
| `kwd_identifier` | VARCHAR(50)        | Identifiant technique en anglais |
| `kwd_created_at` | TIMESTAMP          | Date de création                 |

### Tables de données (T\_)

#### `t_notifications_ntf`

Table principale des notifications/événements.

| Colonne                   | Type               | Description                       |
| ------------------------- | ------------------ | --------------------------------- |
| `ntf_id`                  | INT AUTO_INCREMENT | Identifiant unique                |
| `cty_id`                  | INT                | ID pays (NULL = global)           |
| `ntp_id`                  | INT                | FK vers type de notification      |
| `ntf_identifier_key`      | VARCHAR(100)       | Clé unique en anglais             |
| `ntf_is_active`           | BOOLEAN            | Statut actif/inactif              |
| `ntf_rrule`               | VARCHAR(500)       | Règle RFC 5545 (iCalendar)        |
| `ntf_rrule_text`          | TEXT               | Description humaine               |
| `ntf_icon`                | VARCHAR(50)        | Override de l'icône               |
| `ntf_usage_count`         | INT                | Compteur d'utilisation            |
| `ntf_needs_annual_update` | BOOLEAN            | Mise à jour annuelle requise      |
| `ntf_last_updated_year`   | INT                | Dernière année mise à jour        |
| `ntf_default_reminder`    | JSON               | Config rappel {time, days_before} |
| `ntf_relevance_score`     | DECIMAL(3,2)       | Score 0.00-9.99                   |

#### `t_ntf_cty_override_nco`

Table d'override des notifications par pays. Permet de surcharger les paramètres par défaut d'une notification pour des spécificités locales.

| Colonne                   | Type               | Description                        |
| ------------------------- | ------------------ | ---------------------------------- |
| `nco_id`                  | INT AUTO_INCREMENT | Identifiant unique de l'override   |
| `ntf_id`                  | INT                | FK vers notification               |
| `cty_id`                  | INT                | FK vers pays                       |
| `nco_is_active`           | BOOLEAN            | Override du statut actif/inactif   |
| `nco_rrule`               | VARCHAR(500)       | Override de la règle RFC 5545      |
| `nco_rrule_text`          | TEXT               | Override de la description humaine |
| `nco_usage_count`         | INT                | Compteur d'utilisation spécifique  |
| `nco_needs_annual_update` | BOOLEAN            | Override du besoin de mise à jour  |
| `nco_last_updated_year`   | INT                | Dernière année de mise à jour      |
| `nco_default_reminder`    | JSON               | Override de la config rappel       |
| `nco_relevance_score`     | DECIMAL(3,2)       | Override du score de pertinence    |
| `nco_created_at`          | TIMESTAMP          | Date de création                   |
| `nco_updated_at`          | TIMESTAMP          | Dernière mise à jour               |

**Logique d'utilisation :**

- Les valeurs dans `t_notifications_ntf` servent de base (valeurs par défaut)
- Créer une entrée dans `t_ntf_cty_override_nco` seulement si nécessaire
- Seuls les champs non-NULL dans l'override remplacent les valeurs par défaut
- Les champs NULL dans l'override utilisent la valeur par défaut

#### `t_users_usr`

Utilisateurs de l'application.

| Colonne          | Type               | Description              |
| ---------------- | ------------------ | ------------------------ |
| `usr_id`         | INT AUTO_INCREMENT | Identifiant unique       |
| `usr_device_id`  | VARCHAR(100)       | UUID de l'appareil       |
| `usr_email`      | VARCHAR(255)       | Email (optionnel)        |
| `usr_created_at` | TIMESTAMP          | Première utilisation     |
| `usr_last_sync`  | TIMESTAMP          | Dernière synchronisation |

#### `t_app_translations_atr`

Clés de traduction de l'interface.

| Colonne       | Type               | Description                  |
| ------------- | ------------------ | ---------------------------- |
| `atr_id`      | INT AUTO_INCREMENT | Identifiant unique           |
| `atr_key`     | VARCHAR(255)       | Clé (section.subsection.key) |
| `atr_context` | VARCHAR(255)       | Contexte pour traducteurs    |

#### `t_translation_versions_tvr`

Gestion des versions pour synchronisation.

| Colonne                     | Type               | Description               |
| --------------------------- | ------------------ | ------------------------- |
| `tvr_id`                    | INT AUTO_INCREMENT | Identifiant unique        |
| `cty_id`                    | INT                | FK vers pays              |
| `lng_id`                    | INT                | FK vers langue            |
| `tvr_current_version`       | VARCHAR(20)        | Version actuelle (v1.0.0) |
| `tvr_has_pending_changes`   | BOOLEAN            | Changements non publiés   |
| `tvr_last_published`        | TIMESTAMP          | Date de publication       |
| `tvr_checksum`              | VARCHAR(64)        | Hash SHA256 du snapshot   |
| `tvr_total_items`           | INT                | Nombre d'éléments         |
| `tvr_snapshot_generated_at` | TIMESTAMP          | Génération du snapshot    |

### Tables de traduction

#### Pattern général

Chaque entité traduisible suit ce pattern :

- Table de traduction par défaut : `t_[entity]_trans_[code]`
- Table d'override par pays : `t_[entity]_trans_override_[code]`

#### `t_ntf_translations_ntr` ⭐ **NOUVELLE STRUCTURE**

Traductions des notifications avec pivot central.

| Colonne           | Type               | Description                                        |
| ----------------- | ------------------ | -------------------------------------------------- |
| `ntr_id`          | INT AUTO_INCREMENT | **ID unique de la traduction** (PK)                |
| `ctl_id`          | INT                | FK vers combinaison pays-langue (NULL = universel) |
| `ntf_id`          | INT                | FK vers notification                               |
| `ntr_label`       | VARCHAR(200)       | Nom traduit                                        |
| `ntr_description` | TEXT               | Description traduite                               |

#### `t_notif_trans_override_nov` ⭐ **ULTRA-SIMPLIFIÉ**

Overrides des traductions de notifications.

| Colonne           | Type               | Description                                |
| ----------------- | ------------------ | ------------------------------------------ |
| `nov_id`          | INT AUTO_INCREMENT | ID unique de l'override (PK)               |
| `ntr_id`          | INT UNIQUE         | **FK vers traduction** (référence directe) |
| `nov_label`       | VARCHAR(200)       | Nom surchargé                              |
| `nov_description` | TEXT               | Description surchargée                     |

**Révolution :** Fini les clés composites ! Un override référence directement une traduction via `ntr_id`.

#### `t_ntp_translations_ntt` ⭐ **NOUVELLE STRUCTURE**

Traductions des types de notifications avec pivot central.

| Colonne           | Type               | Description                                        |
| ----------------- | ------------------ | -------------------------------------------------- |
| `ntt_id`          | INT AUTO_INCREMENT | **ID unique de la traduction** (PK)                |
| `ctl_id`          | INT                | FK vers combinaison pays-langue (NULL = universel) |
| `ntp_id`          | INT                | FK vers type de notification                       |
| `ntt_label`       | VARCHAR(100)       | Nom traduit du type                                |
| `ntt_description` | TEXT               | Description traduite                               |

#### `t_ntp_translations_override_nto` ⭐ **ULTRA-SIMPLIFIÉ**

Overrides des traductions de types de notifications.

| Colonne           | Type               | Description                                |
| ----------------- | ------------------ | ------------------------------------------ |
| `nto_id`          | INT AUTO_INCREMENT | ID unique de l'override (PK)               |
| `ntt_id`          | INT UNIQUE         | **FK vers traduction** (référence directe) |
| `nto_label`       | VARCHAR(100)       | Nom surchargé du type                      |
| `nto_description` | TEXT               | Description surchargée                     |

#### `t_nkw_translations_kwt`

Traductions des keywords.

| Colonne    | Type        | Description     |
| ---------- | ----------- | --------------- |
| `kwd_id`   | INT         | FK vers keyword |
| `lng_id`   | INT         | FK vers langue  |
| `kwt_term` | VARCHAR(50) | Terme traduit   |

#### `t_atr_translations_atv` / `t_atv_override_ato`

Traductions des clés d'interface.

| Colonne          | Type | Description                  |
| ---------------- | ---- | ---------------------------- |
| `atr_id`         | INT  | FK vers clé de traduction    |
| `lng_id`         | INT  | FK vers langue               |
| `cty_id`         | INT  | FK vers pays (override only) |
| `a[tt/to]_value` | TEXT | Valeur traduite              |

### Tables de jointure (TJ\_)

#### `tj_country_languages_ctl` ⭐ **PIVOT CENTRAL**

**Table pivot centrale** pour les combinaisons pays-langue avec identifiant unique.

| Colonne  | Type               | Description                          |
| -------- | ------------------ | ------------------------------------ |
| `ctl_id` | INT AUTO_INCREMENT | **ID unique de la combinaison** (PK) |
| `cty_id` | INT                | FK vers pays                         |
| `lng_id` | INT                | FK vers langue                       |

**Rôle clé :** Cette table centralise toutes les combinaisons pays-langue valides et fournit un identifiant unique (`ctl_id`) utilisé dans toutes les tables de traduction, simplifiant drastiquement la structure des overrides.

#### `tj_ntf_keywords_nkw`

Association notifications-keywords.

| Colonne  | Type | Description          |
| -------- | ---- | -------------------- |
| `ntf_id` | INT  | FK vers notification |
| `kwd_id` | INT  | FK vers keyword      |

### Tables de synchronisation

#### `t_sync_snapshots_sss`

Snapshots pré-calculés pour mobile.

| Colonne               | Type               | Description              |
| --------------------- | ------------------ | ------------------------ |
| `sss_id`              | INT AUTO_INCREMENT | Identifiant unique       |
| `cty_id`              | INT                | FK vers pays             |
| `lng_id`              | INT                | FK vers langue           |
| `sss_version`         | VARCHAR(20)        | Version (v1.0.0)         |
| `sss_data`            | LONGBLOB           | JSON compressé GZIP      |
| `sss_checksum`        | VARCHAR(64)        | Hash SHA256              |
| `sss_item_count`      | INT                | Nombre de notifications  |
| `sss_size_bytes`      | INT                | Taille avant compression |
| `sss_size_compressed` | INT                | Taille après compression |
| `sss_created_at`      | TIMESTAMP          | Date de création         |

### Tables de suivi et logs

#### `t_migration_log_mig`

Log des migrations de données.

| Colonne           | Type                      | Description                 |
| ----------------- | ------------------------- | --------------------------- |
| `mig_id`          | INT AUTO_INCREMENT        | Identifiant unique          |
| `mig_version`     | VARCHAR(20)               | Version de migration        |
| `mig_description` | TEXT                      | Description de la migration |
| `mig_executed_at` | TIMESTAMP                 | Date d'exécution            |
| `mig_duration_ms` | INT                       | Durée en millisecondes      |
| `mig_status`      | ENUM('SUCCESS', 'FAILED') | Statut                      |

### Relations principales (Post-Migration)

```
Countries ←N:N→ Languages (via tj_country_languages_ctl)
    ↓
tj_country_languages_ctl (ctl_id) ⭐ PIVOT CENTRAL
    ↓
Notifications → Notif Types
    ↓        ↘
t_*_translations_* (ctl_id)   Keywords ←N:N→ (via tj_ntf_keywords_nkw)
    ↓                             ↓
t_*_trans_override_* (ref_id)  Translations
```

**Architecture simplifiée :**

1. **Pivot central** : `tj_country_languages_ctl` avec `ctl_id` unique
2. **Traductions** : Référencent le pivot via `ctl_id`
3. **Overrides** : Référencent directement la traduction via `ntr_id`, `ntt_id`, etc.

**Flux de données :**

```
Pays + Langue → ctl_id → Traduction → Override (optionnel)
```

## 🔧 Migration et maintenance

### 🚀 Migration 2025 : Pivot Central

#### Contexte de la Migration

**Problème initial :** Structure complexe avec clés composites multiples

```sql
-- AVANT : Triple clé composite
t_notif_trans_override_nov (ntf_id, cty_id, lng_id) -- PK composite
```

**Solution :** Centralisation via pivot `ctl_id`

```sql
-- APRÈS : Structure simplifiée
tj_country_languages_ctl (ctl_id) → t_ntf_translations_ntr (ctl_id) → t_ntf_translations_override_nov (ntr_id)
```

#### Données Migrées (Résultats)

- **8** combinaisons pays-langue centralisées dans `tj_country_languages_ctl`
- **1077** traductions notifications migrées vers nouvelle structure
- **265** traductions types de notifications migrées
- **265** overrides types migrés avec références directes
- **0** overrides notifications (table vide, normal)

#### Avantages Concrets Post-Migration

1. **Performance** : Réduction des jointures de 3 à 1 table
2. **Simplicité** : Création d'override en 1 ligne au lieu de 3 clés
3. **Cohérence** : Combinaisons pays-langue garanties valides
4. **Maintenance** : Structure évolutive et lisible

#### Exemple Concret : Création d'Override

**Avant Migration (Complexe) :**

```sql
INSERT INTO t_notif_trans_override_nov (ntf_id, cty_id, lng_id, nov_label)
VALUES (123, 1, 2, 'Noël Suisse Allemand'); -- Il faut connaître pays ET langue
```

**Après Migration (Simple) :**

```sql
INSERT INTO t_notif_trans_override_nov (ntr_id, nov_label)
VALUES (456, 'Noël Suisse Allemand'); -- Référence directe à la traduction
```

### Procédures stockées

#### `generate_snapshot(country_id, language_id, version)`

Génère un snapshot complet pour un pays/langue.

```sql
DELIMITER //
CREATE PROCEDURE generate_snapshot(
    IN p_country_id INT,
    IN p_language_id INT,
    IN p_version VARCHAR(20)
)
BEGIN
    DECLARE snapshot_data JSON;
    DECLARE compressed_data LONGBLOB;
    DECLARE checksum VARCHAR(64);

    -- Génération des données JSON
    SELECT JSON_OBJECT(
        'version', p_version,
        'country', c.cty_code,
        'language', l.lng_code,
        'generated', NOW(),
        'notifications', (SELECT JSON_ARRAYAGG(notification_data) FROM v_mobile_export WHERE cty_id = p_country_id),
        'translations', (SELECT JSON_OBJECT(...) FROM translations)
    ) INTO snapshot_data
    FROM tr_countries_cty c, tr_languages_lng l
    WHERE c.cty_id = p_country_id AND l.lng_id = p_language_id;

    -- Compression et insertion
    SET compressed_data = COMPRESS(snapshot_data);
    SET checksum = SHA2(compressed_data, 256);

    INSERT INTO t_sync_snapshots_sss (
        cty_id, lng_id, sss_version, sss_data,
        sss_checksum, sss_size_bytes, sss_size_compressed
    ) VALUES (
        p_country_id, p_language_id, p_version, compressed_data,
        checksum, LENGTH(snapshot_data), LENGTH(compressed_data)
    );
END //
DELIMITER ;
```

#### `publish_translation_version(country_id, language_id)`

Publie une nouvelle version et génère le snapshot.

```sql
DELIMITER //
CREATE PROCEDURE publish_translation_version(
    IN p_country_id INT,
    IN p_language_id INT
)
BEGIN
    DECLARE current_version VARCHAR(20);
    DECLARE new_version VARCHAR(20);

    -- Récupération version actuelle
    SELECT tvr_current_version INTO current_version
    FROM t_translation_versions_tvr
    WHERE cty_id = p_country_id AND lng_id = p_language_id;

    -- Incrémentation
    SET new_version = increment_version(current_version);

    -- Mise à jour
    UPDATE t_translation_versions_tvr
    SET tvr_current_version = new_version,
        tvr_last_published = NOW(),
        tvr_has_pending_changes = FALSE
    WHERE cty_id = p_country_id AND lng_id = p_language_id;

    -- Génération snapshot
    CALL generate_snapshot(p_country_id, p_language_id, new_version);
END //
DELIMITER ;
```

#### `increment_version(current_version)`

Incrémente un numéro de version sémantique.

```sql
DELIMITER //
CREATE FUNCTION increment_version(current_version VARCHAR(20))
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE major_num INT;
    DECLARE minor_num INT;
    DECLARE patch_num INT;

    -- Extraction des numéros (v1.2.3 -> 1, 2, 3)
    SET major_num = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(current_version, '.', 1), 'v', -1) AS UNSIGNED);
    SET minor_num = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(current_version, '.', 2), '.', -1) AS UNSIGNED);
    SET patch_num = CAST(SUBSTRING_INDEX(current_version, '.', -1) AS UNSIGNED);

    -- Incrémentation patch
    SET patch_num = patch_num + 1;

    RETURN CONCAT('v', major_num, '.', minor_num, '.', patch_num);
END //
DELIMITER ;
```

### Vues utiles

#### `v_mobile_export` ⭐ **MISE À JOUR POST-MIGRATION**

Vue pour l'export des données mobile avec traductions (nouvelle architecture).

```sql
CREATE VIEW v_mobile_export AS
SELECT
    n.ntf_id,
    n.ntf_identifier_key,
    n.cty_id,
    c.cty_code,
    n.ntp_id,
    nt.ntp_identifier_key as category,
    n.ntf_is_active,
    n.ntf_rrule,
    n.ntf_rrule_text,
    n.ntf_icon,
    n.ntf_relevance_score,
    n.ntf_default_reminder,
    -- Traductions avec fallback (nouvelle architecture)
    COALESCE(nov.nov_label, ntr.ntr_label, n.ntf_identifier_key) as label,
    COALESCE(nov.nov_description, ntr.ntr_description) as description,
    ctl.ctl_id,
    l.lng_code
FROM t_notifications_ntf n
LEFT JOIN tr_countries_cty c ON n.cty_id = c.cty_id
LEFT JOIN tr_notif_types_ntp nt ON n.ntp_id = nt.ntp_id
LEFT JOIN tj_country_languages_ctl ctl ON (n.cty_id = ctl.cty_id OR n.cty_id IS NULL)
LEFT JOIN tr_languages_lng l ON ctl.lng_id = l.lng_id
LEFT JOIN t_ntf_translations_ntr ntr ON n.ntf_id = ntr.ntf_id
    AND (ntr.ctl_id = ctl.ctl_id OR ntr.ctl_id IS NULL)
LEFT JOIN t_ntf_translations_override_nov nov ON ntr.ntr_id = nov.ntr_id
WHERE n.ntf_is_active = TRUE;
```

**Améliorations :**

- Utilisation du pivot central `ctl_id`
- Jointure simplifiée pour les overrides via `ntr_id`
- Support des notifications universelles (`ctl_id IS NULL`)

#### `v_notif_translations` ⭐ **MISE À JOUR POST-MIGRATION**

Vue des traductions avec fallback automatique (nouvelle architecture).

```sql
CREATE VIEW v_notif_translations AS
SELECT
    n.ntf_id,
    ntr.ntr_id,
    ctl.ctl_id,
    l.lng_id,
    l.lng_code,
    c.cty_id,
    c.cty_code,
    COALESCE(nov.nov_label, ntr.ntr_label, n.ntf_identifier_key) as label,
    COALESCE(nov.nov_description, ntr.ntr_description) as description,
    CASE WHEN nov.nov_label IS NOT NULL THEN 'override'
         WHEN ntr.ntr_label IS NOT NULL THEN 'default'
         ELSE 'fallback' END as translation_source
FROM t_notifications_ntf n
CROSS JOIN tr_languages_lng l
LEFT JOIN tr_countries_cty c ON n.cty_id = c.cty_id
LEFT JOIN tj_country_languages_ctl ctl ON c.cty_id = ctl.cty_id AND l.lng_id = ctl.lng_id
LEFT JOIN t_ntf_translations_ntr ntr ON n.ntf_id = ntr.ntf_id
    AND (ntr.ctl_id = ctl.ctl_id OR ntr.ctl_id IS NULL)
LEFT JOIN t_ntf_translations_override_nov nov ON ntr.ntr_id = nov.ntr_id;
```

**Améliorations :**

- Utilisation du pivot central `ctl_id` pour les jointures
- Référence directe aux overrides via `ntr_id`
- Support des traductions universelles (`ctl_id IS NULL`)
- Exposition des IDs internes pour faciliter les requêtes

#### `v_app_translations`

Vue des traductions d'interface avec fallback.

```sql
CREATE VIEW v_app_translations AS
SELECT
    a.atr_id,
    a.atr_key,
    l.lng_id,
    l.lng_code,
    c.cty_id,
    c.cty_code,
    COALESCE(ato.ato_value, att.att_value, a.atr_key) as value,
    CASE WHEN ato.ato_value IS NOT NULL THEN 'override'
         WHEN att.att_value IS NOT NULL THEN 'default'
         ELSE 'fallback' END as translation_source
FROM t_app_translations_atr a
CROSS JOIN tr_languages_lng l
LEFT JOIN tr_countries_cty c ON 1=1
LEFT JOIN t_atr_translations_atv att ON a.atr_id = att.atr_id AND l.lng_id = att.lng_id
LEFT JOIN t_atv_override_ato ato ON a.atr_id = ato.atr_id
    AND c.cty_id = ato.cty_id
    AND l.lng_id = ato.lng_id;
```

### Index recommandés

```sql
-- Index pour les performances de recherche
CREATE INDEX idx_notifs_active ON t_notifications_ntf(ntf_is_active);
CREATE INDEX idx_notifs_country ON t_notifications_ntf(cty_id);
CREATE INDEX idx_notifs_type ON t_notifications_ntf(ntp_id);
CREATE INDEX idx_notifs_identifier ON t_notifications_ntf(ntf_identifier_key);

-- Index pour les traductions
CREATE INDEX idx_notif_trans_notif_lang ON t_ntf_translations_ntr(ntf_id, lng_id);
CREATE INDEX idx_notif_override_notif_country_lang ON t_ntf_translations_override_nov(ntf_id, cty_id, lng_id);

-- Index pour les overrides par pays
CREATE INDEX idx_ntf_cty_override_ntf_id ON t_ntf_cty_override_nco(ntf_id);
CREATE INDEX idx_ntf_cty_override_cty_id ON t_ntf_cty_override_nco(cty_id);
CREATE INDEX idx_ntf_cty_override_active ON t_ntf_cty_override_nco(nco_is_active);
CREATE INDEX idx_ntf_cty_override_relevance ON t_ntf_cty_override_nco(nco_relevance_score);

-- Index pour la synchronisation
CREATE INDEX idx_snapshots_country_lang_version ON t_sync_snapshots_sss(cty_id, lng_id, sss_version);
CREATE INDEX idx_versions_country_lang ON t_translation_versions_tvr(cty_id, lng_id);

-- Index pour les utilisateurs
CREATE INDEX idx_users_device ON t_users_usr(usr_device_id);
CREATE INDEX idx_users_last_sync ON t_users_usr(usr_last_sync);
```

## 🚀 Utilisation

### Génération d'un snapshot

```sql
CALL generate_snapshot(2, 1, 'v1.0.0'); -- Belgique, Français
```

### Publication d'une nouvelle version

```sql
CALL publish_translation_version(2, 1); -- Incrémente automatiquement la version
```

### Vérification des versions

```sql
SELECT c.cty_code, l.lng_code, tvr.tvr_current_version, tvr.tvr_last_published
FROM t_translation_versions_tvr tvr
JOIN tr_countries_cty c ON tvr.cty_id = c.cty_id
JOIN tr_languages_lng l ON tvr.lng_id = l.lng_id
ORDER BY c.cty_code, l.lng_code;
```

### Requête de traduction avec fallback (Post-Migration)

```sql
-- Récupération d'une notification avec traduction (nouvelle architecture)
SELECT
    n.ntf_identifier_key,
    vt.label,
    vt.description,
    vt.translation_source,
    vt.ntr_id,
    vt.ctl_id
FROM t_notifications_ntf n
JOIN v_notif_translations vt ON n.ntf_id = vt.ntf_id
WHERE n.ntf_identifier_key = 'new_year'
  AND vt.cty_code = 'BE'
  AND vt.lng_code = 'fr';
```

### Exemples d'opérations courantes (Post-Migration)

#### Récupérer une notification avec ses overrides par pays

```sql
-- Récupérer une notification avec fallback sur les overrides par pays
SELECT
    n.ntf_id,
    n.ntf_identifier_key,
    c.cty_code,
    -- Utiliser l'override si disponible, sinon la valeur par défaut
    COALESCE(nco.nco_is_active, n.ntf_is_active) as is_active,
    COALESCE(nco.nco_rrule, n.ntf_rrule) as rrule,
    COALESCE(nco.nco_relevance_score, n.ntf_relevance_score) as relevance_score,
    COALESCE(nco.nco_default_reminder, n.ntf_default_reminder) as default_reminder,
    -- Indiquer la source de la valeur
    CASE WHEN nco.nco_id IS NOT NULL THEN 'country_override' ELSE 'default' END as value_source
FROM t_notifications_ntf n
LEFT JOIN t_ntf_cty_override_nco nco ON n.ntf_id = nco.ntf_id
LEFT JOIN tr_countries_cty c ON nco.cty_id = c.cty_id
WHERE n.ntf_identifier_key = 'new_year'
ORDER BY c.cty_code;
```

#### Créer un override par pays

```sql
-- Créer un override pour la France avec des paramètres spécifiques
INSERT INTO t_ntf_cty_override_nco (
    ntf_id,
    cty_id,
    nco_is_active,
    nco_relevance_score,
    nco_default_reminder
) VALUES (
    (SELECT ntf_id FROM t_notifications_ntf WHERE ntf_identifier_key = 'new_year'),
    (SELECT cty_id FROM tr_countries_cty WHERE cty_code = 'FR'),
    true,
    9.50,
    JSON_OBJECT('time', '00:00', 'days_before', 1)
);
```

#### Créer un override de traduction

```sql
-- 1. Trouver la traduction à surcharger
SELECT ntr_id, ntr_label, ntr_description
FROM t_ntf_translations_ntr ntr
JOIN tj_country_languages_ctl ctl ON ntr.ctl_id = ctl.ctl_id
JOIN tr_countries_cty c ON ctl.cty_id = c.cty_id
JOIN tr_languages_lng l ON ctl.lng_id = l.lng_id
WHERE ntr.ntf_id = 123 AND c.cty_code = 'BE' AND l.lng_code = 'fr';

-- 2. Créer l'override (ultra-simple !)
INSERT INTO t_notif_trans_override_nov (ntr_id, nov_label, nov_description)
VALUES (456, 'Nouvel An Belge', 'Célébration du Nouvel An en Belgique');
```

#### Lister toutes les combinaisons pays-langue

```sql
SELECT
    ctl.ctl_id,
    c.cty_code,
    c.cty_name,
    l.lng_code,
    l.lng_name
FROM tj_country_languages_ctl ctl
JOIN tr_countries_cty c ON ctl.cty_id = c.cty_id
JOIN tr_languages_lng l ON ctl.lng_id = l.lng_id
ORDER BY c.cty_code, l.lng_code;
```

#### Vérifier les traductions manquantes

```sql
-- Notifications sans traduction pour une combinaison pays-langue
SELECT
    n.ntf_identifier_key,
    c.cty_code,
    l.lng_code
FROM t_notifications_ntf n
CROSS JOIN tj_country_languages_ctl ctl
JOIN tr_countries_cty c ON ctl.cty_id = c.cty_id
JOIN tr_languages_lng l ON ctl.lng_id = l.lng_id
LEFT JOIN t_ntf_translations_ntr ntr ON n.ntf_id = ntr.ntf_id
    AND (ntr.ctl_id = ctl.ctl_id OR ntr.ctl_id IS NULL)
WHERE ntr.ntr_id IS NULL
  AND n.ntf_is_active = TRUE
ORDER BY c.cty_code, l.lng_code, n.ntf_identifier_key;
```

## 📝 Bonnes pratiques (Post-Migration)

### Générales

1. **Toujours utiliser les préfixes** de colonnes (3 lettres)
2. **Documenter via COMMENT** sur chaque colonne
3. **Utiliser les procédures** pour les opérations complexes
4. **Versionner** chaque modification de données
5. **Utiliser les vues** pour simplifier les requêtes complexes
6. **Optimiser avec les index** appropriés
7. **Valider les données** avant insertion
8. **Monitorer les performances** des snapshots
9. **Sauvegarder** avant les migrations importantes

### Spécifiques à la nouvelle architecture

10. **Utiliser le pivot central** : Toujours passer par `ctl_id` pour les traductions
11. **Références directes** : Créer les overrides via `ntr_id`, `ntt_id`, etc.
12. **Support universel** : Gérer les traductions avec `ctl_id IS NULL`
13. **Cohérence garantie** : Ne jamais créer de combinaisons pays-langue invalides
14. **Performance optimisée** : Privilégier les jointures via ID unique plutôt que clés composites

### Exemples de bonnes pratiques

#### ✅ Correct : Création d'override

```sql
-- Utiliser la référence directe
INSERT INTO t_notif_trans_override_nov (ntr_id, nov_label)
VALUES (456, 'Nouvelle traduction');
```

#### ❌ Incorrect : Ancienne méthode

```sql
-- Ne plus utiliser les clés composites
INSERT INTO t_notif_trans_override_nov (ntf_id, cty_id, lng_id, nov_label)
VALUES (123, 1, 2, 'Nouvelle traduction'); -- Structure obsolète
```

#### ✅ Correct : Requête avec fallback

```sql
-- Utiliser les vues optimisées
SELECT label, description, translation_source
FROM v_notif_translations
WHERE ntf_id = ? AND ctl_id = ?;
```

#### ✅ Correct : Gestion des traductions universelles

```sql
-- Inclure les traductions universelles
SELECT * FROM t_ntf_translations_ntr
WHERE ntf_id = ? AND (ctl_id = ? OR ctl_id IS NULL)
ORDER BY ctl_id DESC; -- Priorité aux traductions spécifiques
```

## ⚙️ Configuration requise

- MySQL 8.0+ (pour support JSON natif)
- UTF8MB4 pour support complet Unicode
- Moteur InnoDB pour les contraintes FK
- Compression GZIP activée pour les snapshots
- `sql_mode` configuré pour la compatibilité
- Timezone UTC pour cohérence globale

## 🔍 Monitoring et maintenance

### Requêtes de monitoring

```sql
-- Taille des snapshots par pays/langue
SELECT
    c.cty_code,
    l.lng_code,
    COUNT(*) as snapshot_count,
    AVG(sss_size_compressed) as avg_size_compressed,
    MAX(sss_created_at) as last_snapshot
FROM t_sync_snapshots_sss s
JOIN tr_countries_cty c ON s.cty_id = c.cty_id
JOIN tr_languages_lng l ON s.lng_id = l.lng_id
GROUP BY c.cty_code, l.lng_code;

-- Notifications sans traduction
SELECT
    n.ntf_identifier_key,
    c.cty_code,
    COUNT(DISTINCT l.lng_id) as languages_available,
    (SELECT COUNT(*) FROM tr_languages_lng) as total_languages
FROM t_notifications_ntf n
LEFT JOIN tr_countries_cty c ON n.cty_id = c.cty_id
LEFT JOIN t_ntf_translations_ntr ntr ON n.ntf_id = ntr.ntf_id
LEFT JOIN tr_languages_lng l ON ntr.lng_id = l.lng_id
GROUP BY n.ntf_id, n.ntf_identifier_key, c.cty_code
HAVING languages_available < total_languages;
```

### Nettoyage automatique

```sql
-- Suppression des anciens snapshots (garder les 5 derniers par pays/langue)
DELETE s1 FROM t_sync_snapshots_sss s1
WHERE s1.sss_id NOT IN (
    SELECT sss_id FROM (
        SELECT sss_id,
               ROW_NUMBER() OVER (PARTITION BY cty_id, lng_id ORDER BY sss_created_at DESC) as rn
        FROM t_sync_snapshots_sss
    ) s2 WHERE s2.rn <= 5
);
```
