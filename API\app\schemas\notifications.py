"""
Schémas Pydantic pour les notifications

Schémas de validation et sérialisation pour les notifications,
types de notifications et keywords avec leurs traductions.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class NotificationTypeTranslationResponse(BaseModel):
    """
    Schéma de réponse pour une traduction de type de notification ⭐ STRUCTURE CORRIGÉE

    Inclut la structure corrigée 2025 : traductions par langue + overrides par pays-langue.
    """
    ntt_id: int = Field(..., description="ID unique de la traduction")
    lng_id: int = Field(..., description="ID de la langue")
    ntp_id: int = Field(..., description="ID du type de notification")
    language_code: str = Field(..., description="Code de la langue")
    ntt_label: Optional[str] = Field(None, description="Nom traduit du type")
    ntt_description: Optional[str] = Field(None, description="Description traduite du type")
    translation_source: str = Field(..., description="Source de la traduction (default/override/fallback)")

    class Config:
        from_attributes = True


class NotificationTypeResponse(BaseModel):
    """
    Schéma de réponse pour un type de notification avec traductions
    """
    ntp_id: int = Field(..., description="Identifiant unique du type")
    ntp_identifier_key: str = Field(..., description="Clé technique unique")
    ntp_icon: Optional[str] = Field(None, description="Nom de l'icône")
    ntp_color: Optional[str] = Field(None, description="Couleur hexa (#FF0000)")
    ntp_bg_color: Optional[str] = Field(None, description="Couleur de fond")
    ntp_display_order: Optional[int] = Field(None, description="Ordre d'affichage")
    ntp_parent_id: Optional[int] = Field(None, description="ID parent pour hiérarchie")
    ntp_level: Optional[int] = Field(None, description="Niveau (0=racine, 1=enfant...)")
    ntp_description_template: Optional[str] = Field(None, description="Template de description")
    ntp_default_enabled: Optional[bool] = Field(None, description="Activé par défaut")
    
    # Traductions
    label: Optional[str] = Field(None, description="Nom traduit (avec fallback)")
    description: Optional[str] = Field(None, description="Description traduite (avec fallback)")
    translation_source: Optional[str] = Field(None, description="Source de la traduction")
    
    # Hiérarchie
    children: List["NotificationTypeResponse"] = Field(default=[], description="Types enfants")
    
    class Config:
        from_attributes = True


class NotificationTranslationResponse(BaseModel):
    """
    Schéma de réponse pour une traduction de notification ⭐ STRUCTURE CORRIGÉE

    Inclut la structure corrigée 2025 : traductions par langue + overrides par pays-langue.
    """
    ntr_id: int = Field(..., description="ID unique de la traduction")
    lng_id: int = Field(..., description="ID de la langue")
    ntf_id: int = Field(..., description="ID de la notification")
    language_code: str = Field(..., description="Code de la langue")
    ntr_label: Optional[str] = Field(None, description="Nom traduit de la notification")
    ntr_description: Optional[str] = Field(None, description="Description traduite")
    translation_source: str = Field(..., description="Source de la traduction (default/override/fallback)")

    class Config:
        from_attributes = True


class NotificationResponse(BaseModel):
    """
    Schéma de réponse pour une notification avec traductions
    """
    ntf_id: int = Field(..., description="Identifiant unique")
    cty_id: Optional[int] = Field(None, description="ID pays (NULL = global)")
    ntp_id: int = Field(..., description="ID du type de notification")
    ntf_identifier_key: str = Field(..., description="Clé unique en anglais")
    ntf_is_active: bool = Field(..., description="Statut actif/inactif")
    ntf_rrule: Optional[str] = Field(None, description="Règle RFC 5545 (iCalendar)")
    ntf_rrule_text: Optional[str] = Field(None, description="Description humaine")
    ntf_icon: Optional[str] = Field(None, description="Override de l'icône")
    ntf_usage_count: Optional[int] = Field(None, description="Compteur d'utilisation")
    ntf_needs_annual_update: Optional[bool] = Field(None, description="Mise à jour annuelle requise")
    ntf_last_updated_year: Optional[int] = Field(None, description="Dernière année mise à jour")
    ntf_default_reminder: Optional[Dict[str, Any]] = Field(None, description="Config rappel JSON")
    ntf_relevance_score: Optional[float] = Field(None, description="Score 0.00-9.99")
    
    # Traductions
    label: Optional[str] = Field(None, description="Nom traduit (avec fallback)")
    description: Optional[str] = Field(None, description="Description traduite (avec fallback)")
    translation_source: Optional[str] = Field(None, description="Source de la traduction")
    
    # Informations du pays (si applicable)
    country_code: Optional[str] = Field(None, description="Code du pays")
    country_name: Optional[str] = Field(None, description="Nom du pays")
    
    class Config:
        from_attributes = True


class NotificationHierarchyResponse(BaseModel):
    """
    Schéma de réponse pour la hiérarchie complète des notifications
    """
    notification_type: NotificationTypeResponse = Field(..., description="Type de notification avec traductions")
    notifications: List[NotificationResponse] = Field(..., description="Notifications de ce type")
    children: List["NotificationHierarchyResponse"] = Field(default=[], description="Sous-types")
    
    class Config:
        from_attributes = True


class NotificationsHierarchyListResponse(BaseModel):
    """
    Schéma de réponse pour la liste complète de la hiérarchie des notifications
    """
    hierarchy: List[NotificationHierarchyResponse] = Field(..., description="Hiérarchie des notifications")
    country_code: str = Field(..., description="Code du pays")
    language_code: str = Field(..., description="Code de la langue")
    total_types: int = Field(..., description="Nombre total de types")
    total_notifications: int = Field(..., description="Nombre total de notifications")
    active_notifications: int = Field(..., description="Nombre de notifications actives")
    
    class Config:
        from_attributes = True


class KeywordTranslationResponse(BaseModel):
    """
    Schéma de réponse pour une traduction de keyword
    """
    kwd_id: int = Field(..., description="ID du keyword")
    kwd_identifier: str = Field(..., description="Identifiant technique")
    lng_id: int = Field(..., description="ID de la langue")
    lng_code: str = Field(..., description="Code de la langue")
    kwt_term: Optional[str] = Field(None, description="Terme traduit")
    
    class Config:
        from_attributes = True


class NotificationKeywordsResponse(BaseModel):
    """
    Schéma de réponse pour les keywords d'une notification
    """
    ntf_id: int = Field(..., description="ID de la notification")
    ntf_identifier_key: str = Field(..., description="Clé de la notification")
    ntf_label: Optional[str] = Field(None, description="Nom traduit de la notification")
    keywords: List[KeywordTranslationResponse] = Field(..., description="Keywords traduits")
    
    class Config:
        from_attributes = True


class CountryLanguageKeywordsResponse(BaseModel):
    """
    Schéma de réponse pour tous les keywords d'un pays/langue
    """
    country_code: str = Field(..., description="Code du pays")
    language_code: str = Field(..., description="Code de la langue")
    notifications: List[NotificationKeywordsResponse] = Field(..., description="Notifications avec leurs keywords")
    total_notifications: int = Field(..., description="Nombre total de notifications")
    total_keywords: int = Field(..., description="Nombre total de keywords uniques")
    
    class Config:
        from_attributes = True


# =====================================================
# SCHÉMAS POUR LES OVERRIDES PAR PAYS
# =====================================================

class NotificationCountryOverrideCreate(BaseModel):
    """
    Schéma de création d'un override de notification par pays
    """
    ntf_id: int = Field(..., description="ID de la notification")
    cty_id: int = Field(..., description="ID du pays")
    nco_is_active: Optional[bool] = Field(None, description="Override du statut actif")
    nco_rrule: Optional[str] = Field(None, description="Override de la règle RFC 5545")
    nco_rrule_text: Optional[str] = Field(None, description="Override de la description humaine")
    nco_usage_count: Optional[int] = Field(None, description="Compteur d'utilisation")
    nco_needs_annual_update: Optional[bool] = Field(None, description="Override du besoin de mise à jour annuelle")
    nco_last_updated_year: Optional[int] = Field(None, description="Dernière année de mise à jour")
    nco_default_reminder: Optional[dict] = Field(None, description="Override de la config rappel")
    nco_relevance_score: Optional[float] = Field(None, description="Override du score de pertinence")

    class Config:
        from_attributes = True


class NotificationCountryOverrideUpdate(BaseModel):
    """
    Schéma de mise à jour d'un override de notification par pays
    """
    nco_is_active: Optional[bool] = Field(None, description="Override du statut actif")
    nco_rrule: Optional[str] = Field(None, description="Override de la règle RFC 5545")
    nco_rrule_text: Optional[str] = Field(None, description="Override de la description humaine")
    nco_usage_count: Optional[int] = Field(None, description="Compteur d'utilisation")
    nco_needs_annual_update: Optional[bool] = Field(None, description="Override du besoin de mise à jour annuelle")
    nco_last_updated_year: Optional[int] = Field(None, description="Dernière année de mise à jour")
    nco_default_reminder: Optional[dict] = Field(None, description="Override de la config rappel")
    nco_relevance_score: Optional[float] = Field(None, description="Override du score de pertinence")

    class Config:
        from_attributes = True


class NotificationCountryOverrideResponse(BaseModel):
    """
    Schéma de réponse pour un override de notification par pays
    """
    nco_id: int = Field(..., description="Identifiant unique de l'override")
    ntf_id: int = Field(..., description="ID de la notification")
    cty_id: int = Field(..., description="ID du pays")
    nco_is_active: Optional[bool] = Field(None, description="Override du statut actif")
    nco_rrule: Optional[str] = Field(None, description="Override de la règle RFC 5545")
    nco_rrule_text: Optional[str] = Field(None, description="Override de la description humaine")
    nco_usage_count: Optional[int] = Field(None, description="Compteur d'utilisation")
    nco_needs_annual_update: Optional[bool] = Field(None, description="Override du besoin de mise à jour annuelle")
    nco_last_updated_year: Optional[int] = Field(None, description="Dernière année de mise à jour")
    nco_default_reminder: Optional[dict] = Field(None, description="Override de la config rappel")
    nco_relevance_score: Optional[float] = Field(None, description="Override du score de pertinence")
    nco_created_at: Optional[datetime] = Field(None, description="Date de création")
    nco_updated_at: Optional[datetime] = Field(None, description="Dernière mise à jour")

    class Config:
        from_attributes = True


class NotificationWithOverridesResponse(BaseModel):
    """
    Schéma de réponse pour une notification avec ses overrides par pays
    """
    notification: NotificationResponse = Field(..., description="Notification de base")
    country_overrides: List[NotificationCountryOverrideResponse] = Field(default=[], description="Overrides par pays")

    class Config:
        from_attributes = True


# Mise à jour des références forward pour la récursion
NotificationTypeResponse.model_rebuild()
NotificationHierarchyResponse.model_rebuild()
