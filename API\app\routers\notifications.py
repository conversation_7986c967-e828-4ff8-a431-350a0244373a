"""
Router pour les notifications

Endpoints pour consulter les notifications, types et keywords avec leurs traductions.
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy import func, and_, or_

from app.database import get_db
from app.models.reference import Country, Language, CountryLanguage
from app.models.notifications import (
    NotificationType, Notification, Keyword,
    NotificationTranslation, NotificationTranslationOverride,
    NotificationTypeTranslation, NotificationTypeTranslationOverride,
    KeywordTranslation, NotificationCountryOverride
)
from app.schemas.notifications import (
    NotificationsHierarchyListResponse,
    NotificationHierarchyResponse,
    NotificationTypeResponse,
    NotificationResponse,
    CountryLanguageKeywordsResponse,
    NotificationKeywordsResponse,
    KeywordTranslationResponse,
    NotificationCountryOverrideCreate,
    NotificationCountryOverrideUpdate,
    NotificationCountryOverrideResponse,
    NotificationWithOverridesResponse
)

# Configuration du logger
logger = logging.getLogger(__name__)

# Création du router
router = APIRouter(prefix="/notifications", tags=["Notifications"])


def get_translated_notification_type(
    notification_type: NotificationType,
    country_id: Optional[int],
    language_id: int,
    db: Session
) -> Dict[str, Any]:
    """
    Récupère un type de notification avec ses traductions (avec fallback) ⭐ POST-MIGRATION.

    Utilise la nouvelle architecture avec pivot central ctl_id et références directes.

    Args:
        notification_type: Instance du type de notification
        country_id: ID du pays (pour les overrides)
        language_id: ID de la langue
        db: Session de base de données

    Returns:
        Dict contenant les données du type avec traductions
    """
    # Récupération des traductions avec fallback (nouvelle architecture)
    override_translation = None
    default_translation = None

    # 1. Trouver le ctl_id pour cette combinaison pays-langue
    ctl_id = None
    if country_id:
        country_language = db.query(CountryLanguage).filter(
            and_(
                CountryLanguage.cty_id == country_id,
                CountryLanguage.lng_id == language_id
            )
        ).first()
        if country_language:
            ctl_id = country_language.ctl_id

    # 2. Chercher la traduction par langue (nouvelle architecture)
    default_translation = db.query(NotificationTypeTranslation).filter(
        and_(
            NotificationTypeTranslation.ntp_id == notification_type.ntp_id,
            NotificationTypeTranslation.lng_id == language_id
        )
    ).first()

    # 3. Chercher un override pour cette traduction et ce pays-langue
    if default_translation and ctl_id:
        override_translation = db.query(NotificationTypeTranslationOverride).filter(
            and_(
                NotificationTypeTranslationOverride.ntt_id == default_translation.ntt_id,
                NotificationTypeTranslationOverride.ctl_id == ctl_id
            )
        ).first()
    
    # Déterminer la traduction à utiliser et sa source
    if override_translation and override_translation.nto_label:
        label = override_translation.nto_label
        description = override_translation.nto_description
        translation_source = "override"
    elif default_translation and default_translation.ntt_label:
        label = default_translation.ntt_label
        description = default_translation.ntt_description
        translation_source = "default"
    else:
        label = notification_type.ntp_identifier_key
        description = None
        translation_source = "fallback"
    
    # Construction du dictionnaire de réponse
    return {
        "ntp_id": notification_type.ntp_id,
        "ntp_identifier_key": notification_type.ntp_identifier_key,
        "ntp_icon": notification_type.ntp_icon,
        "ntp_color": notification_type.ntp_color,
        "ntp_bg_color": notification_type.ntp_bg_color,
        "ntp_display_order": notification_type.ntp_display_order,
        "ntp_parent_id": notification_type.ntp_parent_id,
        "ntp_level": notification_type.ntp_level,
        "ntp_description_template": notification_type.ntp_description_template,
        "ntp_default_enabled": notification_type.ntp_default_enabled,
        "label": label,
        "description": description,
        "translation_source": translation_source,
        "children": []
    }


def get_translated_notification(
    notification: Notification,
    country_id: Optional[int],
    language_id: int,
    db: Session
) -> Dict[str, Any]:
    """
    Récupère une notification avec ses traductions (avec fallback) ⭐ POST-MIGRATION.

    Utilise la nouvelle architecture avec pivot central ctl_id et références directes.

    Args:
        notification: Instance de la notification
        country_id: ID du pays (pour les overrides)
        language_id: ID de la langue
        db: Session de base de données

    Returns:
        Dict contenant les données de la notification avec traductions
    """
    # Récupération des traductions avec fallback (nouvelle architecture)
    override_translation = None
    default_translation = None

    # 1. Trouver le ctl_id pour cette combinaison pays-langue
    ctl_id = None
    if country_id:
        country_language = db.query(CountryLanguage).filter(
            and_(
                CountryLanguage.cty_id == country_id,
                CountryLanguage.lng_id == language_id
            )
        ).first()
        if country_language:
            ctl_id = country_language.ctl_id

    # 2. Chercher la traduction par langue (nouvelle architecture)
    default_translation = db.query(NotificationTranslation).filter(
        and_(
            NotificationTranslation.ntf_id == notification.ntf_id,
            NotificationTranslation.lng_id == language_id
        )
    ).first()

    # 3. Chercher un override pour cette traduction et ce pays-langue
    if default_translation and ctl_id:
        override_translation = db.query(NotificationTranslationOverride).filter(
            and_(
                NotificationTranslationOverride.ntr_id == default_translation.ntr_id,
                NotificationTranslationOverride.ctl_id == ctl_id
            )
        ).first()
    
    # Déterminer la traduction à utiliser et sa source
    if override_translation and override_translation.nov_label:
        label = override_translation.nov_label
        description = override_translation.nov_description
        translation_source = "override"
    elif default_translation and default_translation.ntr_label:
        label = default_translation.ntr_label
        description = default_translation.ntr_description
        translation_source = "default"
    else:
        label = notification.ntf_identifier_key
        description = None
        translation_source = "fallback"
    
    # Informations du pays si applicable
    country_code = None
    country_name = None
    if notification.country:
        country_code = notification.country.cty_code
        country_name = notification.country.cty_name
    
    # Construction du dictionnaire de réponse
    return {
        "ntf_id": notification.ntf_id,
        "cty_id": notification.cty_id,
        "ntp_id": notification.ntp_id,
        "ntf_identifier_key": notification.ntf_identifier_key,
        "ntf_is_active": notification.ntf_is_active,
        "ntf_rrule": notification.ntf_rrule,
        "ntf_rrule_text": notification.ntf_rrule_text,
        "ntf_icon": notification.ntf_icon,
        "ntf_usage_count": notification.ntf_usage_count,
        "ntf_needs_annual_update": notification.ntf_needs_annual_update,
        "ntf_last_updated_year": notification.ntf_last_updated_year,
        "ntf_default_reminder": notification.ntf_default_reminder,
        "ntf_relevance_score": float(notification.ntf_relevance_score) if notification.ntf_relevance_score else None,
        "label": label,
        "description": description,
        "translation_source": translation_source,
        "country_code": country_code,
        "country_name": country_name
    }


def build_notification_hierarchy(
    notification_types: List[NotificationType],
    notifications_by_type: Dict[int, List[Notification]],
    country_id: Optional[int],
    language_id: int,
    db: Session,
    parent_id: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Construit récursivement la hiérarchie des types de notifications.
    
    Args:
        notification_types: Liste de tous les types de notifications
        notifications_by_type: Dictionnaire des notifications groupées par type
        country_id: ID du pays
        language_id: ID de la langue
        db: Session de base de données
        parent_id: ID du parent (None pour les racines)
        
    Returns:
        Liste hiérarchique des types avec leurs notifications
    """
    hierarchy = []
    
    # Filtrer les types pour ce niveau
    current_level_types = [
        nt for nt in notification_types 
        if nt.ntp_parent_id == parent_id
    ]
    
    # Trier par ordre d'affichage
    current_level_types.sort(key=lambda x: x.ntp_display_order or 0)
    
    for notification_type in current_level_types:
        # Récupérer les traductions du type
        type_data = get_translated_notification_type(
            notification_type, country_id, language_id, db
        )
        
        # Récupérer les notifications de ce type
        type_notifications = notifications_by_type.get(notification_type.ntp_id, [])
        translated_notifications = [
            get_translated_notification(notif, country_id, language_id, db)
            for notif in type_notifications
        ]
        
        # Construire récursivement les enfants
        children = build_notification_hierarchy(
            notification_types, notifications_by_type, 
            country_id, language_id, db, notification_type.ntp_id
        )
        
        # Les enfants sont déjà des dictionnaires corrects
        type_data["children"] = [child["notification_type"] for child in children]

        hierarchy_item = {
            "notification_type": type_data,
            "notifications": translated_notifications,
            "children": children
        }
        
        hierarchy.append(hierarchy_item)
    
    return hierarchy


@router.get(
    "/hierarchy",
    response_model=NotificationsHierarchyListResponse,
    summary="Hiérarchie complète des notifications",
    description="""
    Retourne la hiérarchie complète des notifications organisées par type/sous-type.
    
    Inclut :
    - Tous les types de notifications avec leur hiérarchie
    - Toutes les notifications de chaque type
    - Traductions complètes avec système de fallback
    - SANS les keywords (pour éviter un fichier trop volumineux)
    
    Les paramètres pays et langue sont obligatoires pour les traductions.
    """
)
async def get_notifications_hierarchy(
    country_code: str = Query(..., description="Code ISO du pays (ex: 'BE', 'FR')"),
    language_code: str = Query(..., description="Code ISO de la langue (ex: 'fr', 'en')"),
    include_inactive: bool = Query(False, description="Inclure les notifications inactives"),
    db: Session = Depends(get_db)
) -> NotificationsHierarchyListResponse:
    """
    Récupère la hiérarchie complète des notifications avec traductions.
    
    Args:
        country_code: Code ISO du pays
        language_code: Code ISO de la langue
        include_inactive: Inclure les notifications inactives
        db: Session de base de données
        
    Returns:
        NotificationsHierarchyListResponse: Hiérarchie complète
        
    Raises:
        HTTPException: Si le pays ou la langue n'existe pas
    """
    try:
        logger.info(f"Récupération hiérarchie notifications pour {country_code}/{language_code}")
        
        # Normaliser les codes
        country_code = country_code.upper()
        language_code = language_code.lower()
        
        # Vérifier l'existence du pays et de la langue
        country = db.query(Country).filter(Country.cty_code == country_code).first()
        if not country:
            raise HTTPException(
                status_code=404,
                detail=f"Pays non trouvé avec le code: {country_code}"
            )
        
        language = db.query(Language).filter(Language.lng_code == language_code).first()
        if not language:
            raise HTTPException(
                status_code=404,
                detail=f"Langue non trouvée avec le code: {language_code}"
            )
        
        # Récupérer tous les types de notifications
        notification_types = db.query(NotificationType).order_by(
            NotificationType.ntp_level,
            NotificationType.ntp_display_order
        ).all()
        
        # Récupérer toutes les notifications
        notifications_query = db.query(Notification).options(
            joinedload(Notification.country)
        )
        
        if not include_inactive:
            notifications_query = notifications_query.filter(Notification.ntf_is_active == True)
        
        # Filtrer par pays (notifications globales + spécifiques au pays)
        notifications_query = notifications_query.filter(
            or_(
                Notification.cty_id == None,  # Notifications globales
                Notification.cty_id == country.cty_id  # Notifications spécifiques au pays
            )
        )
        
        notifications = notifications_query.all()
        
        # Grouper les notifications par type
        notifications_by_type = {}
        for notification in notifications:
            if notification.ntp_id not in notifications_by_type:
                notifications_by_type[notification.ntp_id] = []
            notifications_by_type[notification.ntp_id].append(notification)
        
        # Construire la hiérarchie
        hierarchy = build_notification_hierarchy(
            notification_types, notifications_by_type,
            country.cty_id, language.lng_id, db
        )
        
        # Calculer les statistiques
        total_types = len(notification_types)
        total_notifications = len(notifications)
        active_notifications = len([n for n in notifications if n.ntf_is_active])
        
        logger.info(f"Hiérarchie construite: {total_types} types, {total_notifications} notifications")
        
        return NotificationsHierarchyListResponse(
            hierarchy=hierarchy,
            country_code=country_code,
            language_code=language_code,
            total_types=total_types,
            total_notifications=total_notifications,
            active_notifications=active_notifications
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la hiérarchie: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération de la hiérarchie des notifications"
        )


@router.get(
    "/types/{type_id}/translations",
    response_model=Dict[str, Any],
    summary="Traductions d'un type de notification",
    description="""
    Retourne toutes les traductions d'un type de notification pour tous les pays et langues.

    Inclut :
    - Traductions par défaut (table t_notif_type_trans_ntt)
    - Overrides par pays (table t_notif_type_trans_ovr_nto)
    - Organisation par pays et langue
    """
)
async def get_notification_type_translations(
    type_id: int = Path(..., description="ID du type de notification"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Récupère toutes les traductions d'un type de notification.

    Args:
        type_id: ID du type de notification
        db: Session de base de données

    Returns:
        Dict contenant toutes les traductions organisées par pays/langue
    """
    # Vérifier que le type existe
    notification_type = db.query(NotificationType).filter(
        NotificationType.ntp_id == type_id
    ).first()

    if not notification_type:
        raise HTTPException(
            status_code=404,
            detail=f"Type de notification avec l'ID {type_id} non trouvé"
        )

    # Récupérer toutes les combinaisons pays-langue disponibles
    country_languages = db.query(
        CountryLanguage,
        Country.cty_code,
        Country.cty_name,
        Language.lng_code,
        Language.lng_name
    ).join(
        Country, CountryLanguage.cty_id == Country.cty_id
    ).join(
        Language, CountryLanguage.lng_id == Language.lng_id
    ).all()

    # Récupérer les traductions de base (par langue uniquement) ⭐ NOUVELLE ARCHITECTURE
    base_translations = db.query(
        NotificationTypeTranslation,
        Language.lng_code,
        Language.lng_name
    ).join(
        Language, NotificationTypeTranslation.lng_id == Language.lng_id
    ).filter(
        NotificationTypeTranslation.ntp_id == type_id
    ).all()

    # Récupérer les overrides (par pays-langue) ⭐ NOUVELLE ARCHITECTURE
    overrides = db.query(
        NotificationTypeTranslationOverride,
        NotificationTypeTranslation,
        CountryLanguage,
        Country.cty_code,
        Language.lng_code
    ).join(
        NotificationTypeTranslation,
        NotificationTypeTranslationOverride.ntt_id == NotificationTypeTranslation.ntt_id
    ).join(
        CountryLanguage, NotificationTypeTranslationOverride.ctl_id == CountryLanguage.ctl_id
    ).join(
        Country, CountryLanguage.cty_id == Country.cty_id
    ).join(
        Language, CountryLanguage.lng_id == Language.lng_id
    ).filter(
        NotificationTypeTranslation.ntp_id == type_id
    ).all()

    # Créer un dictionnaire des traductions de base par langue
    base_translations_dict = {}
    for translation, lang_code, lang_name in base_translations:
        base_translations_dict[lang_code] = {
            "ntt_id": translation.ntt_id,
            "lng_id": translation.lng_id,
            "label": translation.ntt_label,
            "description": translation.ntt_description
        }

    # Créer un dictionnaire des overrides par pays-langue
    overrides_dict = {}
    for override, translation, country_language, country_code, lang_code in overrides:
        key = f"{country_code}-{lang_code}"
        overrides_dict[key] = {
            "nto_id": override.nto_id,
            "ctl_id": override.ctl_id,
            "label": override.nto_label,
            "description": override.nto_description
        }

    # Organiser les traductions pour chaque combinaison pays-langue
    translations = []

    for country_language, country_code, country_name, lang_code, lang_name in country_languages:
        override_key = f"{country_code}-{lang_code}"

        # Vérifier s'il y a un override pour cette combinaison
        if override_key in overrides_dict:
            override = overrides_dict[override_key]
            label = override["label"]
            description = override["description"]
            translation_source = "override"
        # Sinon, utiliser la traduction de base pour cette langue
        elif lang_code in base_translations_dict:
            base_translation = base_translations_dict[lang_code]
            label = base_translation["label"]
            description = base_translation["description"]
            translation_source = "default"
        # Fallback vers la clé technique
        else:
            label = notification_type.ntp_identifier_key
            description = None
            translation_source = "fallback"

        translations.append({
            "ctl_id": country_language.ctl_id,
            "country_code": country_code,
            "country_name": country_name,
            "language_code": lang_code,
            "language_name": lang_name,
            "label": label,
            "description": description,
            "translation_source": translation_source,
            "has_base_translation": lang_code in base_translations_dict,
            "has_override": override_key in overrides_dict
        })

    return {
        "type_id": type_id,
        "type_key": notification_type.ntp_identifier_key,
        "translations": translations,
        "total_translations": len(translations),
        "base_translations_count": len(base_translations_dict),
        "overrides_count": len(overrides_dict)
    }


@router.put(
    "/types/{type_id}/translations/{language_code}",
    response_model=Dict[str, Any],
    summary="Créer/Modifier une traduction de base",
    description="""
    Crée ou modifie une traduction de base pour un type de notification dans une langue donnée.

    Les traductions de base sont universelles pour une langue (pas spécifiques à un pays).
    """
)
async def update_notification_type_base_translation(
    type_id: int = Path(..., description="ID du type de notification"),
    language_code: str = Path(..., description="Code de la langue (ex: 'fr', 'en')"),
    label: Optional[str] = Query(None, description="Libellé traduit"),
    description: Optional[str] = Query(None, description="Description traduite"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Crée ou modifie une traduction de base pour un type de notification.
    """
    # Vérifier que le type existe
    notification_type = db.query(NotificationType).filter(
        NotificationType.ntp_id == type_id
    ).first()

    if not notification_type:
        raise HTTPException(
            status_code=404,
            detail=f"Type de notification avec l'ID {type_id} non trouvé"
        )

    # Vérifier qu'au moins un champ est fourni
    if label is None and description is None:
        raise HTTPException(
            status_code=400,
            detail="Au moins un champ (label ou description) doit être fourni"
        )

    # Vérifier que la langue existe
    language = db.query(Language).filter(Language.lng_code == language_code).first()
    if not language:
        raise HTTPException(
            status_code=404,
            detail=f"Langue '{language_code}' non trouvée"
        )

    # Chercher une traduction existante
    existing_translation = db.query(NotificationTypeTranslation).filter(
        and_(
            NotificationTypeTranslation.ntp_id == type_id,
            NotificationTypeTranslation.lng_id == language.lng_id
        )
    ).first()

    if existing_translation:
        # Modifier la traduction existante (seulement les champs fournis)
        if label is not None:
            existing_translation.ntt_label = label
        if description is not None:
            existing_translation.ntt_description = description
        existing_translation.ntt_updated_at = func.now()
        action = "updated"
        translation_id = existing_translation.ntt_id
    else:
        # Créer une nouvelle traduction
        new_translation = NotificationTypeTranslation(
            ntp_id=type_id,
            lng_id=language.lng_id,
            ntt_label=label,
            ntt_description=description
        )
        db.add(new_translation)
        db.flush()  # Pour obtenir l'ID
        action = "created"
        translation_id = new_translation.ntt_id

    db.commit()

    return {
        "action": action,
        "translation_id": translation_id,
        "type_id": type_id,
        "language_code": language_code,
        "label": label,
        "description": description
    }


@router.put(
    "/types/{type_id}/overrides/{country_code}/{language_code}",
    response_model=Dict[str, Any],
    summary="Créer/Modifier un override local",
    description="""
    Crée ou modifie un override spécifique pour une combinaison pays-langue.

    Les overrides permettent d'avoir des traductions spécifiques à un pays pour une langue donnée.
    """
)
async def update_notification_type_override(
    type_id: int = Path(..., description="ID du type de notification"),
    country_code: str = Path(..., description="Code du pays (ex: 'FR', 'BE')"),
    language_code: str = Path(..., description="Code de la langue (ex: 'fr', 'en')"),
    label: Optional[str] = Query(None, description="Libellé override"),
    description: Optional[str] = Query(None, description="Description override"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Crée ou modifie un override pour un type de notification.
    """
    # Vérifier qu'au moins un champ est fourni
    if label is None and description is None:
        raise HTTPException(
            status_code=400,
            detail="Au moins un champ (label ou description) doit être fourni"
        )

    # Vérifier que le type existe
    notification_type = db.query(NotificationType).filter(
        NotificationType.ntp_id == type_id
    ).first()

    if not notification_type:
        raise HTTPException(
            status_code=404,
            detail=f"Type de notification avec l'ID {type_id} non trouvé"
        )

    # Vérifier que la combinaison pays-langue existe
    country_language = db.query(CountryLanguage).join(
        Country, CountryLanguage.cty_id == Country.cty_id
    ).join(
        Language, CountryLanguage.lng_id == Language.lng_id
    ).filter(
        and_(
            Country.cty_code == country_code,
            Language.lng_code == language_code
        )
    ).first()

    if not country_language:
        raise HTTPException(
            status_code=404,
            detail=f"Combinaison pays-langue '{country_code}-{language_code}' non trouvée"
        )

    # Vérifier qu'une traduction de base existe pour cette langue
    base_translation = db.query(NotificationTypeTranslation).filter(
        and_(
            NotificationTypeTranslation.ntp_id == type_id,
            NotificationTypeTranslation.lng_id == country_language.lng_id
        )
    ).first()

    if not base_translation:
        raise HTTPException(
            status_code=400,
            detail=f"Aucune traduction de base trouvée pour la langue '{language_code}'. Créez d'abord une traduction de base."
        )

    # Chercher un override existant
    existing_override = db.query(NotificationTypeTranslationOverride).filter(
        and_(
            NotificationTypeTranslationOverride.ntt_id == base_translation.ntt_id,
            NotificationTypeTranslationOverride.ctl_id == country_language.ctl_id
        )
    ).first()

    if existing_override:
        # Modifier l'override existant (seulement les champs fournis)
        if label is not None:
            existing_override.nto_label = label
        if description is not None:
            existing_override.nto_description = description
        existing_override.nto_updated_at = func.now()
        action = "updated"
        override_id = existing_override.nto_id
    else:
        # Créer un nouvel override
        new_override = NotificationTypeTranslationOverride(
            ntt_id=base_translation.ntt_id,
            ctl_id=country_language.ctl_id,
            nto_label=label,
            nto_description=description
        )
        db.add(new_override)
        db.flush()  # Pour obtenir l'ID
        action = "created"
        override_id = new_override.nto_id

    db.commit()

    return {
        "action": action,
        "override_id": override_id,
        "type_id": type_id,
        "country_code": country_code,
        "language_code": language_code,
        "label": label,
        "description": description
    }


@router.put(
    "/types/{type_id}/appearance",
    response_model=Dict[str, Any],
    summary="Modifier l'apparence d'un type",
    description="""
    Modifie l'apparence d'un type de notification (icône, couleurs).
    """
)
async def update_notification_type_appearance(
    type_id: int = Path(..., description="ID du type de notification"),
    icon: Optional[str] = Query(None, description="Nom de l'icône"),
    color: Optional[str] = Query(None, description="Couleur principale (#RRGGBB)"),
    bg_color: Optional[str] = Query(None, description="Couleur de fond (#RRGGBB)"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Modifie l'apparence d'un type de notification.
    """
    # Vérifier que le type existe
    notification_type = db.query(NotificationType).filter(
        NotificationType.ntp_id == type_id
    ).first()

    if not notification_type:
        raise HTTPException(
            status_code=404,
            detail=f"Type de notification avec l'ID {type_id} non trouvé"
        )

    # Mettre à jour les champs modifiés
    updated_fields = []
    if icon is not None:
        notification_type.ntp_icon = icon
        updated_fields.append("icon")

    if color is not None:
        notification_type.ntp_color = color
        updated_fields.append("color")

    if bg_color is not None:
        notification_type.ntp_bg_color = bg_color
        updated_fields.append("bg_color")

    if not updated_fields:
        raise HTTPException(
            status_code=400,
            detail="Aucun champ d'apparence fourni pour la mise à jour"
        )

    db.commit()

    return {
        "action": "updated",
        "type_id": type_id,
        "updated_fields": updated_fields,
        "icon": notification_type.ntp_icon,
        "color": notification_type.ntp_color,
        "bg_color": notification_type.ntp_bg_color
    }


@router.delete(
    "/types/{type_id}/overrides/{country_code}/{language_code}",
    response_model=Dict[str, Any],
    summary="Supprimer un override local",
    description="""
    Supprime un override spécifique pour une combinaison pays-langue.

    Après suppression, la traduction de base sera utilisée pour cette combinaison.
    """
)
async def delete_notification_type_override(
    type_id: int = Path(..., description="ID du type de notification"),
    country_code: str = Path(..., description="Code du pays (ex: 'FR', 'BE')"),
    language_code: str = Path(..., description="Code de la langue (ex: 'fr', 'en')"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Supprime un override pour un type de notification.
    """
    # Vérifier que la combinaison pays-langue existe
    country_language = db.query(CountryLanguage).join(
        Country, CountryLanguage.cty_id == Country.cty_id
    ).join(
        Language, CountryLanguage.lng_id == Language.lng_id
    ).filter(
        and_(
            Country.cty_code == country_code,
            Language.lng_code == language_code
        )
    ).first()

    if not country_language:
        raise HTTPException(
            status_code=404,
            detail=f"Combinaison pays-langue '{country_code}-{language_code}' non trouvée"
        )

    # Chercher la traduction de base
    base_translation = db.query(NotificationTypeTranslation).filter(
        and_(
            NotificationTypeTranslation.ntp_id == type_id,
            NotificationTypeTranslation.lng_id == country_language.lng_id
        )
    ).first()

    if not base_translation:
        raise HTTPException(
            status_code=404,
            detail=f"Aucune traduction de base trouvée pour le type {type_id} en langue '{language_code}'"
        )

    # Chercher l'override à supprimer
    override_to_delete = db.query(NotificationTypeTranslationOverride).filter(
        and_(
            NotificationTypeTranslationOverride.ntt_id == base_translation.ntt_id,
            NotificationTypeTranslationOverride.ctl_id == country_language.ctl_id
        )
    ).first()

    if not override_to_delete:
        raise HTTPException(
            status_code=404,
            detail=f"Aucun override trouvé pour '{country_code}-{language_code}'"
        )

    # Supprimer l'override
    db.delete(override_to_delete)
    db.commit()

    return {
        "action": "deleted",
        "type_id": type_id,
        "country_code": country_code,
        "language_code": language_code,
        "message": f"Override supprimé pour '{country_code}-{language_code}'"
    }


@router.get(
    "/keywords",
    response_model=CountryLanguageKeywordsResponse,
    summary="Keywords des notifications par pays/langue",
    description="""
    Retourne les keywords des notifications avec leurs traductions.

    Paramètres obligatoires :
    - country_code : Code ISO du pays
    - language_code : Code ISO de la langue

    Paramètre optionnel :
    - notification_id : ID d'une notification spécifique

    Si notification_id n'est pas spécifié, retourne tous les keywords
    de toutes les notifications du pays/langue.
    """
)
async def get_notifications_keywords(
    country_code: str = Query(..., description="Code ISO du pays (ex: 'BE', 'FR')"),
    language_code: str = Query(..., description="Code ISO de la langue (ex: 'fr', 'en')"),
    notification_id: Optional[int] = Query(None, description="ID d'une notification spécifique"),
    include_inactive: bool = Query(False, description="Inclure les notifications inactives"),
    db: Session = Depends(get_db)
) -> CountryLanguageKeywordsResponse:
    """
    Récupère les keywords des notifications avec traductions.

    Args:
        country_code: Code ISO du pays
        language_code: Code ISO de la langue
        notification_id: ID d'une notification spécifique (optionnel)
        include_inactive: Inclure les notifications inactives
        db: Session de base de données

    Returns:
        CountryLanguageKeywordsResponse: Keywords avec traductions

    Raises:
        HTTPException: Si le pays, la langue ou la notification n'existe pas
    """
    try:
        logger.info(f"Récupération keywords pour {country_code}/{language_code}, notification: {notification_id}")

        # Normaliser les codes
        country_code = country_code.upper()
        language_code = language_code.lower()

        # Vérifier l'existence du pays et de la langue
        country = db.query(Country).filter(Country.cty_code == country_code).first()
        if not country:
            raise HTTPException(
                status_code=404,
                detail=f"Pays non trouvé avec le code: {country_code}"
            )

        language = db.query(Language).filter(Language.lng_code == language_code).first()
        if not language:
            raise HTTPException(
                status_code=404,
                detail=f"Langue non trouvée avec le code: {language_code}"
            )

        # Construire la requête de base pour les notifications
        notifications_query = db.query(Notification).options(
            selectinload(Notification.keywords).selectinload(Keyword.translations)
        )

        if not include_inactive:
            notifications_query = notifications_query.filter(Notification.ntf_is_active == True)

        # Filtrer par pays (notifications globales + spécifiques au pays)
        notifications_query = notifications_query.filter(
            or_(
                Notification.cty_id == None,  # Notifications globales
                Notification.cty_id == country.cty_id  # Notifications spécifiques au pays
            )
        )

        # Filtrer par notification spécifique si demandé
        if notification_id:
            notifications_query = notifications_query.filter(Notification.ntf_id == notification_id)

            # Vérifier que la notification existe
            if not notifications_query.first():
                raise HTTPException(
                    status_code=404,
                    detail=f"Notification non trouvée avec l'ID: {notification_id}"
                )

        notifications = notifications_query.all()

        # Construire la réponse
        notification_keywords_list = []
        total_keywords_set = set()

        for notification in notifications:
            # Récupérer la traduction de la notification
            notification_data = get_translated_notification(
                notification, country.cty_id, language.lng_id, db
            )

            # Récupérer les keywords traduits
            keywords_translated = []
            for keyword in notification.keywords:
                # Chercher la traduction du keyword pour cette langue
                keyword_translation = db.query(KeywordTranslation).filter(
                    and_(
                        KeywordTranslation.kwd_id == keyword.kwd_id,
                        KeywordTranslation.lng_id == language.lng_id
                    )
                ).first()

                keyword_data = {
                    "kwd_id": keyword.kwd_id,
                    "kwd_identifier": keyword.kwd_identifier,
                    "lng_id": language.lng_id,
                    "lng_code": language.lng_code,
                    "kwt_term": keyword_translation.kwt_term if keyword_translation else keyword.kwd_identifier
                }

                keywords_translated.append(keyword_data)
                total_keywords_set.add(keyword.kwd_id)

            notification_keywords = {
                "ntf_id": notification.ntf_id,
                "ntf_identifier_key": notification.ntf_identifier_key,
                "ntf_label": notification_data["label"],
                "keywords": keywords_translated
            }

            notification_keywords_list.append(notification_keywords)

        logger.info(f"Trouvé {len(notifications)} notifications avec {len(total_keywords_set)} keywords uniques")

        return CountryLanguageKeywordsResponse(
            country_code=country_code,
            language_code=language_code,
            notifications=notification_keywords_list,
            total_notifications=len(notifications),
            total_keywords=len(total_keywords_set)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des keywords: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des keywords"
        )


# =====================================================
# ENDPOINTS POUR LES OVERRIDES PAR PAYS
# =====================================================

@router.get(
    "/overrides/country/{country_code}",
    response_model=List[NotificationCountryOverrideResponse],
    summary="Récupérer les overrides d'un pays",
    description="Récupère tous les overrides de notifications pour un pays donné"
)
async def get_country_overrides(
    country_code: str = Path(..., description="Code ISO du pays (FR, BE...)"),
    db: Session = Depends(get_db)
):
    """
    Récupère tous les overrides de notifications pour un pays donné.
    """
    try:
        # Vérifier que le pays existe
        country = db.query(Country).filter(Country.cty_code == country_code.upper()).first()
        if not country:
            raise HTTPException(status_code=404, detail=f"Pays '{country_code}' non trouvé")

        # Récupérer les overrides
        overrides = db.query(NotificationCountryOverride).filter(
            NotificationCountryOverride.cty_id == country.cty_id
        ).all()

        return overrides

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des overrides: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des overrides"
        )


@router.get(
    "/{notification_id}/overrides",
    response_model=List[NotificationCountryOverrideResponse],
    summary="Récupérer les overrides d'une notification",
    description="Récupère tous les overrides par pays pour une notification donnée"
)
async def get_notification_overrides(
    notification_id: int = Path(..., description="ID de la notification"),
    db: Session = Depends(get_db)
):
    """
    Récupère tous les overrides par pays pour une notification donnée.
    """
    try:
        # Vérifier que la notification existe
        notification = db.query(Notification).filter(Notification.ntf_id == notification_id).first()
        if not notification:
            raise HTTPException(status_code=404, detail=f"Notification {notification_id} non trouvée")

        # Récupérer les overrides
        overrides = db.query(NotificationCountryOverride).filter(
            NotificationCountryOverride.ntf_id == notification_id
        ).all()

        return overrides

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des overrides: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération des overrides"
        )


@router.post(
    "/overrides",
    response_model=NotificationCountryOverrideResponse,
    status_code=201,
    summary="Créer un override par pays",
    description="Crée un nouvel override de notification pour un pays spécifique"
)
async def create_country_override(
    override_data: NotificationCountryOverrideCreate,
    db: Session = Depends(get_db)
):
    """
    Crée un nouvel override de notification pour un pays spécifique.
    """
    try:
        # Vérifier que la notification existe
        notification = db.query(Notification).filter(Notification.ntf_id == override_data.ntf_id).first()
        if not notification:
            raise HTTPException(status_code=404, detail=f"Notification {override_data.ntf_id} non trouvée")

        # Vérifier que le pays existe
        country = db.query(Country).filter(Country.cty_id == override_data.cty_id).first()
        if not country:
            raise HTTPException(status_code=404, detail=f"Pays {override_data.cty_id} non trouvé")

        # Vérifier qu'un override n'existe pas déjà
        existing_override = db.query(NotificationCountryOverride).filter(
            and_(
                NotificationCountryOverride.ntf_id == override_data.ntf_id,
                NotificationCountryOverride.cty_id == override_data.cty_id
            )
        ).first()

        if existing_override:
            raise HTTPException(
                status_code=409,
                detail=f"Un override existe déjà pour la notification {override_data.ntf_id} et le pays {override_data.cty_id}"
            )

        # Créer l'override
        new_override = NotificationCountryOverride(**override_data.model_dump())
        db.add(new_override)
        db.commit()
        db.refresh(new_override)

        logger.info(f"Override créé: notification {override_data.ntf_id}, pays {override_data.cty_id}")
        return new_override

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur lors de la création de l'override: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la création de l'override"
        )


@router.put(
    "/overrides/{override_id}",
    response_model=NotificationCountryOverrideResponse,
    summary="Modifier un override par pays",
    description="Modifie un override de notification existant"
)
async def update_country_override(
    override_id: int = Path(..., description="ID de l'override"),
    override_data: NotificationCountryOverrideUpdate = None,
    db: Session = Depends(get_db)
):
    """
    Modifie un override de notification existant.
    """
    try:
        # Récupérer l'override existant
        existing_override = db.query(NotificationCountryOverride).filter(
            NotificationCountryOverride.nco_id == override_id
        ).first()

        if not existing_override:
            raise HTTPException(status_code=404, detail=f"Override {override_id} non trouvé")

        # Mettre à jour les champs fournis
        update_data = override_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(existing_override, field, value)

        db.commit()
        db.refresh(existing_override)

        logger.info(f"Override {override_id} mis à jour")
        return existing_override

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur lors de la mise à jour de l'override: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la mise à jour de l'override"
        )


@router.delete(
    "/overrides/{override_id}",
    status_code=204,
    summary="Supprimer un override par pays",
    description="Supprime un override de notification"
)
async def delete_country_override(
    override_id: int = Path(..., description="ID de l'override"),
    db: Session = Depends(get_db)
):
    """
    Supprime un override de notification.
    """
    try:
        # Récupérer l'override existant
        existing_override = db.query(NotificationCountryOverride).filter(
            NotificationCountryOverride.nco_id == override_id
        ).first()

        if not existing_override:
            raise HTTPException(status_code=404, detail=f"Override {override_id} non trouvé")

        # Supprimer l'override
        db.delete(existing_override)
        db.commit()

        logger.info(f"Override {override_id} supprimé")
        return None

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Erreur lors de la suppression de l'override: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la suppression de l'override"
        )


@router.get(
    "/{notification_id}/with-overrides",
    response_model=NotificationWithOverridesResponse,
    summary="Récupérer une notification avec ses overrides",
    description="Récupère une notification avec tous ses overrides par pays"
)
async def get_notification_with_overrides(
    notification_id: int = Path(..., description="ID de la notification"),
    db: Session = Depends(get_db)
):
    """
    Récupère une notification avec tous ses overrides par pays.
    """
    try:
        # Récupérer la notification avec ses overrides
        notification = db.query(Notification).options(
            selectinload(Notification.country_overrides)
        ).filter(Notification.ntf_id == notification_id).first()

        if not notification:
            raise HTTPException(status_code=404, detail=f"Notification {notification_id} non trouvée")

        # Construire la réponse
        return NotificationWithOverridesResponse(
            notification=notification,
            country_overrides=notification.country_overrides
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la notification avec overrides: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Erreur lors de la récupération de la notification avec overrides"
        )
